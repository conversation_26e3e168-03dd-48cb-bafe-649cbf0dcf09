CREATE TABLE IF NOT EXISTS `product_tag_parent` (
													`id` int(11) NOT NULL AUTO_INCREMENT,
	`internalName` varchar(250) COLLATE utf8_bin NOT NULL DEFAULT '',
	`customFieldsJson` longtext COLLATE utf8_bin,
	`customContentJson` longtext COLLATE utf8_bin,
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

ALTER TABLE `product_tag_parent`
	ADD COLUMN `sort` INT NOT NULL DEFAULT '0' AFTER `customContentJson`;

CREATE TABLE IF NOT EXISTS `product_tag` (
											 `id` int(11) NOT NULL AUTO_INCREMENT,
	`parentId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`name` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`public` int(11) DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext COLLATE utf8_bin,
	`customContentJson` longtext COLLATE utf8_bin,
	PRIMARY KEY (`id`) USING BTREE,
	KEY `FK_product_tag_mutation` (`mutationId`) USING BTREE,
	KEY `FK_product_tag_product_tag_parent` (`parentId`) USING BTREE,
	CONSTRAINT `FK_product_tag_product_tag_parent` FOREIGN KEY (`parentId`) REFERENCES `product_tag_parent` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_product_tag_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


CREATE TABLE IF NOT EXISTS `product_x_product_tag_parent` (
															  `id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`productTagParentId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	KEY `FK_product_x_product_tag_parent_product` (`productId`),
	KEY `FK_product_x_product_tag_parent_product_tag_parent` (`productTagParentId`),
	CONSTRAINT `FK_product_x_product_tag_parent_product` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_product_x_product_tag_parent_product_tag_parent` FOREIGN KEY (`productTagParentId`) REFERENCES `product_tag_parent` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


ALTER TABLE `product_tag_parent`
	CHANGE COLUMN `internalName` `internalName` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_czech_ci' AFTER `id`;


 ALTER TABLE `product_tag`
	ADD COLUMN `template` VARCHAR(250) NULL DEFAULT '' AFTER `customContentJson`,
	ADD COLUMN `nameAnchor` VARCHAR(250) NULL DEFAULT '' AFTER `template`,
	ADD COLUMN `nameTitle` VARCHAR(250) NULL DEFAULT '' AFTER `nameAnchor`,
	ADD COLUMN `description` TEXT NULL DEFAULT '' AFTER `nameTitle`,
	ADD COLUMN `keywords` TEXT NULL DEFAULT '' AFTER `description`;
