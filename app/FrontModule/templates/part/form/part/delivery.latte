{* chci zboží doručit na jinou než fakturační adresu *}
<div class="f-open" data-controller="ToggleCheckbox">
	<p>
		<label class="inp-item inp-item--checkbox">
			<input n:name="deliveryTab" value="1" class="inp-item__inp" data-action="change->ToggleCheckbox#changeClass"/>
			<span class="inp-item__text">
				{_'title_other_delivery_form'}
			</span>
		</label>
	</p>
	<div n:class="f-open__box, $form['deliveryTab']->value ? is-open" data-ToggleCheckbox-target="box">
		{if $user->loggedIn && $userEntity}
			<div class="f-open f-delivery" data-controller="ToggleRadio">
				<ul class="f-delivery__list">
					{var $k = 0}
					{if $userEntity->customAddress}
						{foreach $userEntity->customAddress as $k=>$address}
							<li n:class="f-delivery__item, $form['customAddress']->errors ? has-error">
								<label class="inp-item inp-item--radio">
									<input type="radio" n:name="customAddress" value="{$k}" class="inp-item__inp" data-ToggleRadio-target="inp" data-action="change->ToggleRadio#changeClass"{if $form['customAddress']->value === $k} checked="checked"{/if}>
									<span class="inp-item__text">
										<strong>
											{if $address->firstname}{$address->firstname}{/if}
											{if $address->lastname}{$address->lastname}{/if}{if $address->company},{/if}
											{if $address->company}{$address->company}{/if}
										</strong>
										<br>
										{if $address->street}
											<span>{$address->street}</span>,
										{/if}
										{if $address->city}
											<span>{$address->city}</span>,
										{/if}
										{if $address->zip}
											<span>{$address->zip}</span>,
										{/if}
										<a n:if="$address->phone" href="tel:{$address->phone|replace:' ', ''}">
											{$address->phone}
										</a>
									</span>
								</label>
							</li>
						{/foreach}
					{/if}
					<li n:class="f-delivery__item, $form['customAddress']->errors ? has-error">
						<label class="inp-item inp-item--radio">
							<input type="radio" n:name="customAddress" id="frm-orderStep2Form-form-customAddress-{$k}" value="XXX" class="inp-item__inp" data-ToggleRadio-target="inp" data-action="change->ToggleRadio#changeClass" data-target="customAddress"{if $form['customAddress']->value === 'XXX'} checked="checked"{/if}>
							<span class="inp-item__text">
								{_'another_address_form'}
							</span>
						</label>
					</li>
				</ul>
				<div n:class="f-open__box, customAddress, $form['customAddress']->value === 'XXX' ? is-open" data-ToggleRadio-target="box">
		{/if}

		{include '../../../../components/inp.latte', form=>$form, name=>dFirstname, required=>true}
		{include '../../../../components/inp.latte', form=>$form, name=>dLastname, required=>true}
		{include '../../../../components/inp.latte', form=>$form, name=>dCompany}
		{include '../../../../components/inp.latte', form=>$form, name=>dPhone}
		{include '../../../../components/inp.latte', form=>$form, name=>dStreet, required=>true}
		{include '../../../../components/inp.latte', form=>$form, name=>dCity, required=>true}
		{include '../../../../components/inp.latte', form=>$form, name=>dZip, required=>true}
		{include '../../../../components/inp.latte', form=>$form, name=>dState}
		{include '../../../../components/inp.latte', form=>$form, name=>dInfo}

		{if $user->loggedIn && $userEntity}
				</div>
			</div>
		{/if}
	</div>
</div>

