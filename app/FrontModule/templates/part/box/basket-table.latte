{default $orderHistory = FALSE}
{varType SuperKoderi\Basket $basket}
{varType App\Model\Order $order}

<div class="c-basket" data-controller="Basket">
	{* produkty *}
	{if $order->products->count() + $order->sets->count()}
		<table class="c-basket__table">
			<thead>
				<tr>
					<th>
						{_title_product_buy}
					</th>
					{* <th>
						{_availability}
					</th> *}
					<th>
						{_amount}
					</th>
					<th>
						{_unit_price_vat} / {_'count_short'}
					</th>
					<th>
						{_total_price_vat}
					</th>
					<th>&nbsp;</th>
				</tr>
			</thead>
			<tbody>
				{foreach $order->parentItems as $productOrderItem}
					{capture $link}
						{plink $productOrderItem->variant, v=>$productOrderItem->variant->id}
					{/capture}

					<tr class="c-basket__row c-basket__row--product">
						{* <td class="c-basket__col c-basket__col--img">
							<a href="{$link}">
								{if $productOrderItem->variant->images && $productOrderItem->variant->images->count()}
									{php $img = $productOrderItem->variant->firstImage->getSize('xxs')}
									<img src="{$img->src}" alt="{$productOrderItem->variant->nameAnchor}" width="{$img->width}" height="{$img->height}" loading="lazy"/>
								{else}
									<img src="/static/img/noimg.svg" alt="" width="40" height="40" loading="lazy"/>
								{/if}
							</a>
						</td> *}
						<td class="c-basket__col c-basket__col--product">
							<div class="c-basket__product">
								<a href="{$link}" class="c-basket__product-img" title="{$productOrderItem->variant->nameAnchor}">
									{if $productOrderItem->variant->images && $productOrderItem->variant->images->count()}
										{php $img = $productOrderItem->variant->firstImage->getSize('xxs')}
										<img src="{$img->src}" alt="{$productOrderItem->variant->nameAnchor}" width="{$img->width}" height="{$img->height}" loading="lazy"/>
									{else}
										<img src="/static/img/noimg.svg" alt="" width="40" height="40" loading="lazy"/>
									{/if}
								</a>
								<a href="{$link}" class="c-basket__product-name">
									{if $productOrderItem->subType == "voucher" || $productOrderItem->subType == "voucherElectronic"}
										{$productOrderItem->name}
									{else}
										{$productOrderItem->variant->nameAnchor}
									{/if}
								</a>
							</div>
						</td>
						{* <td class="c-basket__col c-basket__col--supplies">
							{if !$productOrderItem->variant->supplies->count()}
								<strong>
									{_not_available}
								</strong>
							{/if}
						</td> *}
						<td class="c-basket__col c-basket__col--count">
							{if !isset($static) || !$static}
								{if strpos($productOrderItem->uniqueKey, "sub") !== FALSE}
									{$productOrderItem->amount}
								{else}
									{php $maxVal = $productOrderItem->variant->totalSupplyCount}
									<span data-controller="InpCount" data-InpCount-min-value="1" data-InpCount-max-value="{$maxVal}">
										<input type="hidden" name="id" value="{$productOrderItem->variant->id}"  />
										<span class="inp-fix inp-count">
											<button type="button" class="inp-count__tool inp-count__tool--minus" data-step="-1" data-action="InpCount#changeValue" data-InpCount-target="toolMinus">
												<span class="u-vhide">-</span>
												{('minus')|icon}
											</button>
											<input type="number" class="inp-text" name="amount[{$productOrderItem->uniqueKey}]" value="{$productOrderItem->amount}" data-InpCount-target="inp" max="{$maxVal}" data-action="change->Basket#submitForm">
											<button type="button" class="inp-count__tool inp-count__tool--plus" data-step="1" data-action="InpCount#changeValue" data-InpCount-target="toolPlus">
												<span class="u-vhide">+</span>
												{('plus')|icon}
											</button>
										</span>
									</span>
								{/if}
							{/if}
						</td>
						<td class="c-basket__col c-basket__col--price c-basket__col--price-unit">
							{$productOrderItem->unitPriceDPH|priceFormat}
						</td>
						<td class="c-basket__col c-basket__col--price">
							<strong>{$productOrderItem->totalPriceDPH|priceFormat}</strong>
						</td>
						<td class="c-basket__col c-basket__col--remove">
							{if !isset($static) || !$static}
								{if strpos($productOrderItem->uniqueKey, "sub") == FALSE}
									<a n:href="remove!, key=>$productOrderItem->uniqueKey" title="{_'remove'}">
										{('close')|icon}
										<span class="u-vhide">{_'remove'}</span>
									</a>
								{/if}
							{/if}
						</td>
					</tr>

					{foreach $productOrderItem->services as $i}
						<tr class="c-basket__row">
							{* <td class="c-basket__col"></td> *}
							<td class="c-basket__col">
								{$i->name}
							</td>
							<td class="c-basket__col">
								{$productOrderItem->amount}
							</td>
							<td class="c-basket__col">
								{$i->unitPriceDPH|priceFormat}
								/ {_service}
							</td>
							<td class="c-basket__col">
								{$i->unitPriceDPH*$productOrderItem->amount|priceFormat}
							</td>
							<td class="c-basket__col">
								{if !isset($static) || !$static}
									<a n:href="removeSub!, key=>$productOrderItem->uniqueKey, subKey=>$i->getItemId()" class="c-basket__remove-link">
										{('close')|icon}
										<span class="u-vhide">{_'remove'}</span>
									</a>
								{/if}
							</td>
						</tr>
					{/foreach}
				{/foreach}
			</tbody>
		</table>
	{/if}


	{* vouchery *}
	{include basketVoucher}

	{* dárky *}
	{if $order->products->count()}
		{foreach $order->products as $productOrderItem}
			{if $productOrderItem->presents}
				{foreach $productOrderItem->presents as $present}
					<p class="c-basket__gift">
						{if isset($present->product->alias)}<a n:href="{$present->product}">{$present->name}</a>{else}{$present->name}{/if}
					</p>
				{/foreach}
			{/if}
		{/foreach}
	{/if}

	{varType SuperKoderi\Basket $basket}
	<div class="f-open b-coupon" data-controller="ToggleCheckbox" n:if="!($order->vouchers->count()) && ! $basket->hasVoucherProductInBasket()">
		<p class="b-coupon__toggle">
			<label class="inp-item inp-item--checkbox">
				<input type="checkbox" name="voucher" value="1" {if isset($vcodeIsError)}checked="checked"{/if} class="inp-item__inp" data-action="change->ToggleCheckbox#changeClass"/>
				<span class="inp-item__text">
					{_'title_have_coupon'}
				</span>
			</label>
		</p>
		<div n:class="f-open__box, b-coupon__box, grid__cell, size--autogrow, isset($vcodeIsError) ? is-open" data-ToggleCheckbox-target="box">{block|strip}
			<span n:class="inp-fix, isset($vcodeIsError) ? has-error">
				<input type="text" class="inp-text" placeholder="{_'title_coupon'}" name="voucherCode" value="" />
			</span>
			<button type="submit" class="btn" name="voucherCheck">
				<span class="btn__text">
					{_'use'}
				</span>
			</button>{/block}
		</div>
	</div>
	<div>
		{include basketTotal}
	</div>
</div>

{define basketTotal}
	<table class="c-basket__total">
		<tr class="c-basket__total--novat">
			<td class="c-basket__total--name">
				{_'total_price'}
			</td>
			<td class="c-basket__total--price">
				<span>
					{$order->totalPrice|priceFormat}
				</span>
			</td>
		</tr>
		<tr class="c-basket__total--vat">
			<td class="c-basket__total--name">
				{_'total_price_vat'}
			</td>
			<td class="c-basket__total--price">
				<strong>
					{$order->totalPriceDPH|priceFormat}
				</strong>
			</td>
		</tr>
	</table>
{/define}

{define basketVoucher}
	{if $order->vouchers->count() || $order->discounts->count()}
		<table class="c-basket__table">
			{* <thead>
				<tr class="c-basket__row">
					<td class="c-basket__col">
						{_title_voucher}
					</td>
					<td class="c-basket__col">
						{_total_price_vat}
					</td>
					<td class="c-basket__col">&nbsp;</td>
				</tr>
			</thead> *}
			<tbody>
				{if $order->vouchers->count()}
					{foreach $order->vouchers as $voucherOrderItem}
						<tr class="c-basket__row">
							<td class="c-basket__col">
								{_'title_voucher'}: {$voucherOrderItem->name}
							</td>
							<td class="c-basket__col c-basket__col--price">
								{$voucherOrderItem->unitPriceDPH|priceFormat}
							</td>
							<td class="c-basket__col c-basket__col--remove">
								{if !isset($static) || !$static}
									<a n:href="removeVoucher, $voucherOrderItem->subType" class="c-basket__remove-link">
										{('close')|icon}
										<span class="u-vhide">{_'remove'}</span>
									</a>
								{/if}
							</td>
						</tr>
					{/foreach}
				{/if}

				{if $order->discounts->count()}
					{foreach $order->discounts as $voucherOrderItem}
						<tr class="c-basket__row">
							<td class="c-basket__col">
								{$voucherOrderItem->name}
							</td>
							<td class="c-basket__col">
								{$voucherOrderItem->unitPriceDPH|priceFormat}
							</td>
							<td class="c-basket__col">&nbsp;</td>
						</tr>
					{/foreach}
				{/if}

			</tbody>
		</table>
	{/if}
{/define}
