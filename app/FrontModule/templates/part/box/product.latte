{default $class = ''}
{default $productTitle = null}
{var $variant = null}
{var $parametersShortArray = ['series', 'year', 'qualityClasses', 'bySugar']}
{default $loading = "lazy"}
{default $showRemoveFromFavorite = false}
{default $count = null}
{default $forceLink = null}

{if $product}

	{* prices *}
	{if $variant}
		{varType App\Model\ProductVariant $variant}
		{var $priceWithVat = $variant->priceWithVat($mutation, $priceLevel, $state)}
		{var $originalPriceWithVat = $variant->originalPriceWithVat($mutation, $priceLevel, $state)}
		{var $discountPriceWithVat = $variant->discountPriceWithVat($mutation, $state)}
		{var $isPriceForm = false}
	{else}
		{* P s vice PV *}
		{varType App\Model\Product $product}
		{var $priceWithVat = $product->priceWithVat($mutation, $priceLevel, $state)}
		{var $originalPriceWithVat = $product->originalPriceWithVat($mutation, $priceLevel, $state)}
		{var $discountPriceWithVat = $product->discountPriceWithVat($mutation, $state)}
		{var $isPriceForm = $product->hasPriceFrom($mutation, $priceLevel, $state)}
	{/if}

	{* content *}
	<article class="b-product link-mask {$class}">
		{if $product->isVariant}
			{php $link = $presenter->link($product, ['v'=>$product->id])}
			{var $variant = $product}
			{var $product = $product->product}
		{else}
			{* je to produkt - obalka*}
			{php $link = $presenter->link($product)}
			{var $activeVariant = $product->activeVariants->fetchAll()}
			{if count($activeVariant) == 1}
				{var $variant = $activeVariant[0]}
			{/if}
		{/if}


		{if $forceLink !== null}
			{var $link = $forceLink}
		{/if}


			<div class="b-product__img">
				<div class="flags flags--main-labels" n:ifcontent>
					<ul class="flags__list" n:ifcontent>
						<li class="flags__item" n:if="$product->getParameterValueByUid('awards')">
							<span style="background-color: #F3A6A6;">{_'product_flag_awards'}</span>
						</li>
						<li class="flags__item" n:if="$product->isNew" title="{_'product_flag_new'}">
							<span style="background-color: #FEDD76;">{_'product_flag_new'}</span>
						</li>
						<li class="flags__item" n:if="$product->isSale" title="{_'product_flag_sale'}">
							<span style="background-color: #D1E2AF;">{_'product_flag_sale'}</span>
						</li>

						<!-- TODO-MEZSUP-5 FE implementace niže uvedené jak získať štítky -->
						{if $product->productTagParents->count()>0}
							<li class="flags__item" n:foreach="$product->productTagParents as $productTagParent">
								<span style="background-color: {$productTagParent->color|noescape}">
									{$productTagParent->getName($mutation)}
								</span>
							</li>
						{/if}

						<li class="flags__item" n:if="$priceWithVat != $originalPriceWithVat">
							{var $discountValue = $priceWithVat/$originalPriceWithVat * 100 - 100}
							<span style="background-color: #D1E2AF;">
								{$discountValue|round}%
							</span>
						</li>
					</ul>
				</div>
				<div class="flags flags--favorite" n:if="isset($userEntity) && $userEntity->userFavoriteProducts->toCollection()->getBy(['product->id' => $product->id]) !== null">
					<span class="flags__item" title="{_'product_favorite'}">
						<span class="u-vhide">{_'product_favorite'}</span>{('icon-favorite')|icon}
					</span>
				</div>
				<div class="b-product__img-wrapper">
					{if $product->firstImage}
						{* {php $img = $product->firstImage->getSize('md')}
						<img src="{$img->src}" alt="" loading="lazy" width="{$img->width}" height="{$img->height}"> *}
						{include $presenter->getTemplatefile('part/core/image.latte'),
							img: $product->firstImage,
							alt: $product->nameAnchor,
							srcset: ['250w' => '', '500w' => '2x'],
							loading: $loading
						}
					{else}
						<img src="/static/img/noimg.svg" alt="" loading="{$loading}" width="250" height="250">
					{/if}
				</div>
			</div>

			<h3 n:tag="$productTitle" class="b-product__title">
				<a href="{$link}" class="b-product__link link-mask__link">
					{if $count !== null} {$count} x {/if}{$product->nameAnchor}{* vzdy nazev produktu (obalky) *}
				</a>
			</h3>

			<p class="b-product__params-short" n:ifcontent>
				{var $params = []}
				{foreach $parametersShortArray as $param}
					{ifset $product->getParameterValueByUid($param)->value}
						{var $params[] = $product->getParameterValueByUid($param)->value}
					{/ifset}
				{/foreach}
				{$params|implode:', '}
			</p>
			{include productAvailability}
			{include productPrice}
	</article>
	<div class="u-ta-c u-mt-xs" n:if="$showRemoveFromFavorite && ($favoriteProduct = $userEntity->userFavoriteProducts->toCollection()->getBy(['product->id' => $product->id]))">
		<a n:href="removeFromFavorite!, id => $favoriteProduct->id" data-naja class="b-favorite">{('heart-solid')|icon}{_"product_remove_favorite"}</a>
	</div>
{/if}

{define productPrice}
	<div class="b-product__price">
		{* {if $variant}
			{varType App\Model\ProductVariant $variant}
			{var $priceWithVat = $variant->priceWithVat($mutation, $priceLevel, $state)}
			{var $originalPriceWithVat = $variant->originalPriceWithVat($mutation, $priceLevel, $state)}
			{var $discountPriceWithVat = $variant->discountPriceWithVat($mutation, $state)}
			{var $isPriceForm = false}
		{else}
			{varType App\Model\Product $product}
			{var $priceWithVat = $product->priceWithVat($mutation, $priceLevel, $state)}
			{var $originalPriceWithVat = $product->originalPriceWithVat($mutation, $priceLevel, $state)}
			{var $discountPriceWithVat = $product->discountPriceWithVat($mutation, $state)}
			{var $isPriceForm = $product->hasPriceFrom($mutation, $priceLevel, $state)}
		{/if} *}

		<s class="b-product__price-before" n:if="$priceWithVat != $originalPriceWithVat">
			{$originalPriceWithVat|priceFormat}
		</s>

		{if $priceWithVat !== false}
			{if $priceWithVat == 0}
				{_price_not_determined}
			{else}
				<span class="b-product__price-main">
					<small n:if="$isPriceForm">
					{_'price_from'}
					</small>
					<strong>
						{$priceWithVat|priceFormat}
					</strong>
					{* {_'price_tax'}<br>
					<small>
						{$price|priceFormat} {_price_without_tax}
					</small> *}
				</span>
			{/if}
		{/if}
	</div>
{/define}

{define productAvailability}
	{if $product->isInStock}
		{if $product->totalSupplyCount < 100}
			{var $text = 'availability_in_stock_over_100', $class = 'is-warning'}
		{else}
			{var $text = 'availability_in_stock', $class = 'is-success'}
		{/if}

		{if !$variant && $product->supplyInfo->someNotAvailable}
			{var $note = 'availability_only_some_variants'}
		{/if}
	{else}
		{var $text = 'availability_soldout', $class = 'is-danger'}
	{/if}

	<div n:class="b-product__availability, $class">
		<strong>
			{_$text|stripHtml}
		</strong>
		<small n:if="isset($note)">({_$note})</small>
	</div>
{/define}
