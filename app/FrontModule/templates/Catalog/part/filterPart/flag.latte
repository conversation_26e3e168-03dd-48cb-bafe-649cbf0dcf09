{default $forceOpen = false}
{default $id = false}

<fieldset class="f-filter__group{if $filterItem->isOpen || $forceOpen} is-open{/if}" data-controller="FilterGroup">
	<legend class="f-filter__title">
		<button type="button" class="btn f-filter__name" data-action="FilterGroup#toggle" id="{$id}-btn" aria-controls="{$id}" aria-expanded="{$filterItem->isOpen || $forceOpen ? 'true' : 'false'}">
			{$filterItem->title|translate}
		</button>
	</legend>
	<div class="f-filter__inner" id="{$id}" role="region" aria-labelledby="{$id}-btn" {if !($filterItem->isOpen || $forceOpen)}hidden=""{/if}>
		<ul class="f-filter__list">
			{include 'flagValue.latte', filterItem=>$filterItem,
				name=>$filterItem->title,
				count=>$filterItem->count,
				isActive=>$filterItem->isActive,
				inputName=>$filterItem->inputName,
				inputValue=>$filterItem->inputValue,
			}
		</ul>
	</div>
</fieldset>
