{block content}
	{snippet basketBig}
		<div class="row-main">
			{dump $order}
			{if $order->parentItems->count()}
					<h1 class="u-vhide">{$object->name}</h1>
					{include '../part/box/steps.latte', step=>1}
					{control orderBasket}
			{else} {* pr<PERSON><PERSON><PERSON>ý k<PERSON>š<PERSON> *}
					{control breadcrumb}
					<h1>
						{* {_mainbasket_empty_title} *}
						{$object->name}
					</h1>
					<div class="u-text-content u-mb-lg">
						<p>
							{_mainbasket_empty_text} <a href="{plink $pages->eshop}">{_eshop}</a>.
						</p>
					</div>
			{/if}
		</div>
	{/snippet}
{/block}

