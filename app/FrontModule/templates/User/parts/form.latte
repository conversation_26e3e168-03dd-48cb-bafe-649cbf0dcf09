{block content}
	<div class="row-main">
		{* {control breadcrumb} *}

		<div class="box box--default box--small u-mb-lg u-mt-lg">

			{include $templates.'/part/box/annot.latte'}

			{foreach $flashes as $flash}
				<div class="message message--{$flash->type}">{_$flash->message}</div>
			{/foreach}

			{if $object->uid == 'userLogin'} {* login *}
			{if $presenter->isAjax()}
				{snippet signInFormAjax}
					{control signInForm}
				{/snippet}
			{else}
				{snippet signInForm}
					{control signInForm}
				{/snippet}
			{/if}
			{elseif $object->uid == 'registration'} {* registration *}
				{control registrationForm}
			{elseif $object->uid == 'lostPassword'} {* lost password *}
				{control lostPasswordForm}
			{elseif $object->uid == 'resetPassword'} {* reset password *}
				{control lostPasswordForm:reset}
			{/if}

		</div>


		{include $templates.'/part/box/content.latte'}
		{include $templates.'/part/attached/images.latte'}
		{include $templates.'/part/attached/videos.latte'}
		{include $templates.'/part/attached/files.latte'}
	</div>


{*include '../../templates/part/tracking/gtm_page.latte', gtmKey=>"userSection", object=>$object*}

