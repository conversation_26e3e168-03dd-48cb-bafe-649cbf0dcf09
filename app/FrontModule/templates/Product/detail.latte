{block content}
{varType App\Model\ProductVariant $variant}
{varType App\Model\Product $product}
{varType App\Model\ProductLocalization $object}

{dump $product}
{* uid parametrov zobrazenych pod nadpisem *}
{var $parametersShortArray = ['qualityClasses']}
{* uid parametru, ktoreho img sa zobrazuje v levem hornim rohu obrazku *}
{var $parameterSeriesLogo = 'viticulture'}

{var $priceVat = $variant->priceVat($mutation, $priceLevel, $state)}

	<div class="row-main">
		{snippet breadcrumbArea}
			{control breadcrumb}
		{/snippet}

		<div class="b-product-detail u-mb-lg">
			<div class="b-product-detail__thumbs">
				<div class="b-product-detail__thumb-wrapper">
					{include $templates.'/part/box/product-gallery.latte'}

					<div class="flags flags--main-labels" n:ifcontent>
						<ul class="flags__list" n:ifcontent>
							<li class="flags__item" n:if="$product->getParameterValueByUid('awards')">
								{('icon-medal')|icon}
							</li>
							<li class="flags__item" n:if="$product->isNew" title="{_'product_flag_new'}">
								<span class="u-vhide">{_'product_flag_new'}</span>
								{('icon-new')|icon}
							</li>
							<li class="flags__item" n:if="$product->isSale" title="{_'product_flag_sale'}">
								<span class="u-vhide">{_'product_flag_sale'}</span>
								{('icon-sale')|icon}
							</li>
							{var $priceWithVat = $product->priceWithVat($mutation, $priceLevel, $state)}
							{var $originalPriceWithVat = $product->originalPriceWithVat($mutation, $priceLevel, $state)}
							<li class="flags__item" n:if="$priceWithVat != $originalPriceWithVat">
								{var $discountValue = $priceWithVat/$originalPriceWithVat * 100 - 100}
								<span class="flags__discount-value">{$discountValue|round}%</span>
							</li>
						</ul>
					</div>
					{ifset $variant->getParameterValueByUid($parameterSeriesLogo)->cf->viticultures->image}
						<div class="flags flags--series">
							{include $templates.'/part/core/image.latte',
								img: $variant->getParameterValueByUid($parameterSeriesLogo)->cf->viticultures->image,
								alt: $variant->getParameterValueByUid($parameterSeriesLogo)->value,
								srcset: ['150w' => '1x', '300w' => '2x'],
								sizes: '150px',
								loading: 'lazy',
								wrapper: false
							}
						</div>
					{/ifset}

					<div class="b-share-buttons--transform" n:ifcontent>
						{include $templates.'/part/box/share-buttons.latte', url: $presenter->link('this', [])}
					</div>
				</div>
				{include flagsParamsAwards}
			</div>
			<div class="b-product-detail__content">
				<h1>{$variant->name}{ifset $variant->getParameterValueByUid('year')->value} {$variant->getParameterValueByUid('year')->value}{/ifset}</h1>
				<p class="b-product-detail__params-short" n:ifcontent n:ifset="$variant->product->detailTitle">{$variant->product->detailTitle}</p>
				<p n:if="$variant->annotation" class="b-product-detail__annot u-text-content u-mb-md">
					{$variant->annotation|texy|noescape}
				</p>

				<div class="b-variants" n:if="$product->activeVariants->count() > 1">
					<h2 class="b-variants__title">
						{_'title_product_all_variants'}
					</h2>
					<ul class="b-variants__list" n:inner-foreach="$product->activeVariants as $activeVariant">
						<li class="b-variants__item">
							<a n:href="$activeVariant, v=>$activeVariant->id" class="b-variants__link{if $variant->id == $activeVariant->id} u-fw-b{/if}">
								{$activeVariant->nameAnchor}
							</a>
						</li>
					</ul>
				</div>
				{if ($priceVat > 0 && ($variant->isInStock && !$product->isVoucher || ($product->isVoucher && $object->voucher && $object->voucher->public)) )}
					<div class="b-product-order u-mb-sm">
						<div class="b-product-order__price">
							{include $templates.'/part/core/price.latte', object=>$variant}
						</div>
						<div class="b-product-order__basket">
							{include $templates.'/part/core/availability.latte', object=>$variant}
							{include $templates.'/part/form/add-to-basket.latte', product=>$product}
						</div>
					</div>
					{if !$product->isVoucher}
						{include messageProductOrderNote, class: 'u-mb-sm'}
					{/if}
				{else}
					<div class="message message--error u-mb-sm">
						<div class="message__image" n:ifcontent>
							{('m-error')|icon}
						</div>
						<div class="u-text-content">
							<p class="message__title" n:ifcontent>{_'msg_old_product_heading'|stripHtml}</p>
							<p>{_'msg_old_product'}</p>
							<p n:if="count($variant->products)">
								<a href="#products" class="btn">
									<span class="btn__text">
										{_'btn_old_product'}
									</span>
								</a>
							</p>
						</div>
					</div>
				{/if}

				<div n:snippet="addProductToFavorite" class="u-mb-md u-text-r">
					{if $user->isLoggedIn()}
						{if $userEntity->userFavoriteProducts->toCollection()->getBy(["product->id" => $product->id]) !== null}
							<a n:href="toggleFavoriteProduct!" data-naja data-naja-history="off" class="b-favorite">{('heart-solid')|icon}<span>{_'product_remove_favorite'}</span></a>
						{else}
							<a n:href="toggleFavoriteProduct!" data-naja data-naja-history="off" class="b-favorite">{('heart-regular')|icon}<span>{_'product_add_favorite'}</span></a>
						{/if}
					{else}
						<a n:href="$pages->userLogin" data-naja data-naja-loader="body" data-naja-modal="snippet--signInFormAjax" data-custom-class="b-modal--small" data-naja-history="off" class="b-favorite">
							{('heart-regular')|icon}<span>{_'product_add_favorite'}</span>
						</a>
					{/if}
				</div>

				{* {var $parameterAcidity = $product->getParameterValueByUid('acidity')} *}
				{if $product->getParameterValueByUid('acidity') || $product->getParameterValueByUid('sugar') || $product->getParameterValueByUid('alcohol') || $product->getParameterValueByUid('extract')}
					{include parametersRange, parameters: ['acidity', 'sugar', 'alcohol', 'extract']}
				{/if}

				{include messageProductNote}
				{control productParameters, except => ['acidity', 'sugar', 'alcohol', 'extract']}
				{include '../part/box/content.latte'}
			</div>
		</div>

		<div id="products" n:if="count($variant->products)"></div>

		{if $product->isSet}
			{include '../part/attached/products.latte',
				products: $product->products,
				counts: $product->productsCounts,
				heading: 'product_products_heading_set'
			}
		{elseif $product->isVoucher}
			{*none*}
		{else}
			{include '../part/attached/products.latte',
				products: $similarProducts,
				heading: 'product_products_heading'
			}
		{/if}

		{include '../part/attached/videos.latte'}
		{include '../part/attached/files.latte'}
	</div>
{/block}

{define flagsParamsAwards}
	<div class="b-awards u-mt-lg" n:ifcontent n:if="$product->getParameterValueByUid('awards')">
		<ul class="b-awards__list" n:inner-foreach="$product->getParameterValueByUid('awards') as $parameter">
			<li n:ifset="$parameter->cf->image->image->entity->url" class="b-awards__item">
				{include $templates.'/part/core/image.latte', img: $parameter->cf->image->image->entity, class: '', srcset: ['100w'], alt: $parameter->value}
			</li>
		</ul>
	</div>
{/define}

{define messageProductNote}
	<div class="message message--warning u-mb-md" n:ifset="$product->cf->product_recommended_meal_message->text" n:ifcontent>
		<div class="message__image" n:ifcontent>
			{('m-fork')|icon}
		</div>
		<div class="u-text-content">
			<p class="message__title" n:ifcontent>{_'message_product_recommended_meal_title'}</p>
			<p>{$product->cf->product_recommended_meal_message->text}</p>
		</div>
	</div>
{/define}

{define messageProductOrderNote}
	<div n:if="!$product->isSet" n:class="message, $class">
		<div class="message__image" n:ifcontent>
			{('m-box')|icon}
		</div>
		<div class="u-text-content">
			<p>{_'message_order_6_pack_bottles'|stripHtml}</p>
		</div>
	</div>
{/define}

{define parametersRange}
	{* {var $parameterValue = $product->getParameterValueByUid($parameter->uid)} *}
	{default $showParametersRange = false}
	<div class="b-parameters-range u-mb-md" n:ifcontent>
		<h2 class="b-parameters-range__title">{_'product_parameters_range_title'}</h2>
		{var $parameterSugar = $product->getParameterValueByUid('sugar') ?? ""}
		{var $parameterSugarArray = $product->sugar}
		<dl n:ifcontent n:inner-foreach="$parameters as $parameterUid">
			{var $parameterValue = $product->getParameterValueByUid($parameterUid)}
			{if
			((isset($parameterValue->parameter->cf->p_values_range->min) && isset($parameterValue->parameter->cf->p_values_range->max))
			||
			($parameterUid == 'sugar'))
			&& isset($parameterValue->value)}

				{if $parameterValue->parameter->uid == 'sugar'}
					{var int $rangeMin = $parameterSugarArray['min']}
					{var int $rangeMax = $parameterSugarArray['max']}
					{var int $rangeValue = str_replace(',', '.', $parameterSugarArray['value'])}
				{else}
					{var int $rangeMin = $parameterValue->parameter->cf->p_values_range->min}
					{var int $rangeMax = $parameterValue->parameter->cf->p_values_range->max}
					{var int $rangeValue = str_replace(',', '.', $parameterValue->value)}
				{/if}
				{var int $rangePercentage = ($rangeValue / ($rangeMin + $rangeMax)) * 100}
				{var $rangeShow = $rangeValue >= $rangeMin && $rangeValue <= $rangeMax}

				<div class="b-parameters-range__item" n:if="$rangeShow">
					<dt class="b-parameters-range__dt" n:ifset="$parameterValue->parameter->title">{$parameterValue->parameter->title}</dt>
					<dd class="b-parameters-range__dd">
						<span class="b-parameters-range__range" data-range-min="{$rangeMin}" data-range-max="{$rangeMax}" data-range-value="{$rangeValue}">
							<span class="b-parameters-range__handle" style="left:{$rangePercentage|number:0}%"></span>
						</span>
						<span class="b-parameters-range__value" n:if="isset($parameterValue->value) && isset($parameterValue->parameter->unit)">{$parameterValue->value}&nbsp;{$parameterValue->parameter->unit}</span>
					</dd>
				</div>
				{var $showParametersRange = true}
			{/if}
		</dl>
	</div>
{/define}
