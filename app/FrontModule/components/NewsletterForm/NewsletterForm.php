<?php declare(strict_types = 1);

namespace SuperKoderi\Components;

use App\Exceptions\UserException;
use App\Model\NewsletterEmailModel;
use App\Model\Orm;
use App\Model\SmartMailing\SmartMailing;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Random;
use SuperKoderi\ConfigService;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\hasAntispamInputTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\MutationHolder;
use SuperKoderi\TranslatorDB;
use Throwable;

/**
 * @property-read DefaultTemplate $template
 */
class NewsletterForm extends UI\Control
{

	use hasMessageForFormComponentTrait;
	use hasAntispamInputTrait;

	private TranslatorDB $translator;

	private ICommonFactory $commonEmailFactory;

	private MutationHolder $mutationHolder;

	protected Orm $orm;

	protected NewsletterEmailModel $newsletterEmailModel;

	public function __construct(
		MutationHolder $mutationHolder,
		TranslatorDB $translator,
		ICommonFactory $commonEmailFactory,
		Orm $orm,
		NewsletterEmailModel $newsletterEmailModel,
		public SmartMailing $smartMailing,
		public ConfigService $configService
	)
	{
		$this->translator = $translator;
		$this->commonEmailFactory = $commonEmailFactory;
		$this->mutationHolder = $mutationHolder;
		$this->orm = $orm;
		$this->newsletterEmailModel = $newsletterEmailModel;
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->pages = $this->mutationHolder->getMutation()->pages;
		$this->template->render(__DIR__ . '/newsletterForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addText('email', 'form_label_email')
			->addRule(Form::EMAIL)
			->setHtmlType('email')
			->setRequired();

		$form->addCheckbox('approve')
			->setRequired('newsletter_approve');

		$form->addSubmit('send');

		$this->antispamPrepare($form);

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}


	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form): void
	{

		$values = $form->getValues();
		$hash = Random::generate(20);

		try {
			$this->newsletterEmailModel->subscribeEmail($values->email, $this->mutationHolder->getMutation(), $hash);
			$this->orm->flush();
			$this->smartMailing->sendContact($values->email, NULL, NULL, $this->configService->getParam("smartmailing", "newsletterListId"), "unsubscribed");

			$this->commonEmailFactory
				->create()
				->send('', $values->email, 'newsletterInfo', ["hash" => $hash], "newsletter");

			$this->flashMessage('newsletterAdded', 'ok');
		} catch (UserException $e) {
			$this->flashMessage('newsletterAlreadyAdded', 'error');
		} catch (Throwable $e) {
			$this->flashMessage('newsletterError', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect("this");
		}
	}

}


interface INewsletterFormFactory
{

	function create(): NewsletterForm;

}
