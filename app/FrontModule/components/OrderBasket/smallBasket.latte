{varType SuperKoderi\Basket $basket}
<div class="b-basket-sm b-header">
	<a class="b-basket-sm__link b-header__link" href="{plink $pages->basket}">
		{* <span class="b-basket-sm__icon b-header__icon">
			{('basket')|icon}
			<span class="b-basket-sm__count b-header__count">
				{$basket->getProductsCount()}
			</span>
		</span> *}
		<span class="b-basket-sm__title">{_'basket_small_title'}</span>
		<span class="b-basket-sm__name b-header__name">
			{if $basket->getProductsCount()}
				{* <span class="b-basket-sm__value">
					{('bottle'|icon)}
					{$basket->getProductsCount()}
				</span> *}
				{block|strip}
				<span class="b-basket-sm__value">
					{('cart-shopping'|icon)}
					{$order->totalPriceDPH|priceFormat}
				</span>
				{/block}
			{else}
				{block|strip}
					<span class="b-basket-sm__value">
						{('cart-shopping'|icon)}
					</span>
				{/block}
				{* <span>{('cart-shopping'|icon: 'u-d-n@xl')}{_basket_empty}</span> *}
			{/if}
		</span>
	</a>
	{* meziviny: nezobrazuje popup- rovno presmeruje na kosik *}
	<div class="b-basket-sm__popup b-header__popup" n:if="false">
		{if $order->parentItems->count() == 0}
			<div class="b-basket-sm__inner">
				<div class="b-basket-sm__content">
					<h2 class="h4">{_basket_empty_title}</h2>
					<p>{_basket_empty_text}</p>
				</div>
			</div>
		{else}
			<form n:name="form" class="b-basket-sm__form">
				{control messageForForm, $flashes, $form}
				{if $order->products->count()}
					<div class="b-basket-sm__group">
						<h2 class="b-basket-sm__title h5">
							<span class="info">
								<span>
									{_basket_product_title}
								</span>
								<span class="info__number">{$order->products->count()}</span>
							</span>
						</h2>
						<div class="b-basket-sm__list">
							{foreach $order->products as $orderItem}
								{if $orderItem->type != 'rent'}
									{include FE_TEMPLATE_DIR .'/part/box/basket-product-small.latte', product=>$orderItem}
								{/if}
							{/foreach}
						</div>
					</div>
				{/if}



				<p class="b-basket-sm__total">
					{_'price_sum'}
					<strong>{$order->totalPriceDPH|priceFormat}</strong>
					{* <span class="b-basket-sm__tax">{_price_tax}</span> *}
				</p>
				<p class="b-basket-sm__btn">
					<button type="submit" n:name="basket" class="btn btn--sm">
						<span class="btn__text">{_'btn_basket'}</span>
					</button>
				</p>
			</form>
		{/if}
	</div>
</div>
