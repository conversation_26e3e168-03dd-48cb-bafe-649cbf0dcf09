
{if $showCanonical}
	{capture $link}{plink '//this', filter=>null}{/capture}
	{php $link = urldecode(htmlspecialchars_decode($link))}
	<link rel="canonical" href="{$link|noescape}">
{/if}
{*{if $prev}*}
{*	{if $prev == 1}*}

{*		{capture $link}{plink '//this', pager-page=>null, filter=>$filter}{/capture}*}
{*		{php $link = urldecode(htmlspecialchars_decode($link))}*}
{*		<link rel=“prev” href="{$link|noescape}" />*}
{*	{else}*}

{*		{capture $link}{plink '//this', pager-page=>$prev, filter=>$filter}{/capture}*}
{*		{php $link = urldecode(htmlspecialchars_decode($link))}*}

{*		<link rel=“prev” href="{$link|noescape}" />*}
{*	{/if}*}

{*{/if}*}
{*{if $next}*}
{*	{capture $link}{plink '//this', pager-page=>$next, filter=>$filter}{/capture}*}
{*	{php $link = urldecode(htmlspecialchars_decode($link))}*}
{*	<link rel=“next” href="{$link|noescape}" />*}
{*{/if}*}
