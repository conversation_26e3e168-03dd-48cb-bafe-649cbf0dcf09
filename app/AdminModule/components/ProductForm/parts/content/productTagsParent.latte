{var $props = [
	title: '<PERSON><PERSON><PERSON>t<PERSON>',
	id: 'productTagParent',
	variant: 'main',
	icon: $templates.'/part/icons/tags.svg',
	classes: ['u-mb-xxs'],
	tags: []
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}
		{foreach $form['productCommon']['productTagParents']->components as $itemKey=>$itemContainer}
			{continueIf $itemKey === 'newItemMarker'}

			{var $url = $urls['searchProductTagParent']->toArray()}

			{var $item = [
				inps: [
					[
						input: $itemContainer['name'],
						placeholder: 'Zadejte interní název příznaku',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $itemContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'

					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url),
				]

			]}
			{php $items[] = $item}
		{/foreach}

		{include $templates . '/part/box/list.latte',
			props: [
				data: [
					controller: 'List',
					List-name-value: 'productTagParent',

				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: true,
				dragdrop: false,
				type: 'input',
				items: $items
				]
		}


	{/block}
{/embed}


