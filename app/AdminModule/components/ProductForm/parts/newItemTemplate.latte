<div class="u-hide">
	{*	<div data-Templates-target="variantOverlay">{% include '@components/templates/overlay-variants.twig' %}</div>*}
	{*	<div data-Templates-target="variantItem">{% include '@components/templates/item-variant.twig' %}</div>*}


	{foreach $mutations as $mutation}

		{var $productLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $mutation])}
		{var $categoryContainer = $form['productLocalizations'][$mutation->id]['categories']['newItemMarker']}
		<div data-Templates-target="categoryItem" data-mutationid="{$mutation->id}">



			{var $url = $urls['searchMutationPage']->toArray()}
			{php $url['params']['mutationId'] = $mutation->id}
			{php $url['params']['templates'] = ['Catalog:default']}



			{include $templates.'/part/box/list-item.latte',
			props: [
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
			],
				inps: [
					[
						placeholder: 'Zadejte název kategorie',
						input: $categoryContainer['name'],
						data: [
							suggestinp-target: 'input',

						]
					],
					[
						input: $categoryContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]

				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						]
					]
				],
				dragdrop: true
			]
			}
		</div>

		{var $videoContainer = $form['productLocalizations'][$mutation->id]['videos']['newItemMarker']}

		<div data-Templates-target="videoItem" data-mutationid="{$mutation->id}">
			{include $templates.'/part/box/list-item.latte',
			props: [
				dragdrop: true,
				inps: [
					[
						input: $videoContainer['link'],
						placeholder: 'Url videa',
					],[
						input: $videoContainer['name'],
						placeholder: 'Název',
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
						action: 'RemoveItem#remove'
					],
				]
				],
				data: [
					controller: 'RemoveItem',
					RemoveItem-target: 'item',
				]
			]
			}
		</div>


		{var $pageContainer = $form['productLocalizations'][$mutation->id]['pages']['newItemMarker']}
		<div data-Templates-target="pageItem" data-mutationid="{$mutation->id}">

			{var $url = $urls['searchMutationPage']->toArray()}
			{php $url['params']['mutationId'] = $mutation->id}

			{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $pageContainer['name'],
						placeholder: 'Zadejte název stránky',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $pageContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
				],
				dragdrop: true
			]
			}
		</div>

	{var $pageContainer = $form['productLocalizations'][$defaultMutation->id]['vouchers']['newItemMarker']}
		<div data-Templates-target="voucherItem" data-mutationid="{$defaultMutation->id}">

			{var $url = $urls['searchVoucher']->toArray()}
			{php $url['params']['mutationId'] = $defaultMutation->id}
			{php $url['params']['kind'] = App\Model\Voucher::KIND_PRODUCT}

			{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $pageContainer['name'],
						placeholder: 'Zadejte název kupónu',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $pageContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
				],
				dragdrop: true
			]
			}
		</div>

		{var $linkContainer = $form['productLocalizations'][$mutation->id]['links']['newItemMarker']}
		<div data-Templates-target="linkItem" data-mutationid="{$mutation->id}">
			{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $linkContainer['link'],
						placeholder: 'Zadejte název stránky',
					],
					[
						input: $linkContainer['name'],
						placeholder: 'Zadejte název odkazu',
					],
					[
						input: $linkContainer['open'],
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem',
					removeitem-target: 'item'
				],
				dragdrop: true
			]
			}
		</div>


		{var $fileContainer = $form['productLocalizations'][$mutation->id]['files']['newItemMarker']}
		<div data-Templates-target="fileItem" data-mutationid="{$mutation->id}">
			{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $fileContainer['fileName'],
						placeholder: 'Zadejte název souboru',
						disabled: true,
						data: [
							file-target: 'nameInput'
						],
					],
					[
						input: $fileContainer['fileId'],
						data: [
							file-target: 'fileId'
						],
						classes: 'u-hide',
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem File',
					action: 'Templates:uploadFile@window->File#upload',
					file-id-value: 'newItemMarker',
					file-url-value: $fileUploadLink,
					removeitem-target: 'item'
				],
				dragdrop: true,
				progress: true,
			]
			}
		</div>
	{/foreach}


	{var $imageContainer = $form['productCommon']['images']['newItemMarker']}
	<div data-Templates-target="imageItem">
		{include $templates.'/part/box/image-item.latte',
			props: [
				dragdrop: true,
				data: [
					controller: 'RemoveItem',
					removeitem-target: 'item',
					removeitem-animation-value: 'fade',
					id: '{imageId}'
				],
				img: '{imageSrc}',
				inps: [
					[
						input: $imageContainer['imageId'],
						type: 'hidden',
						data: [
							image-id: '',
						]
					]
				],
				btns: [
					[
						icon: $templates.'/part/icons/pencil-alt.svg',
						tooltip: 'Editovat',
						data: [
							controller: 'Toggle',
							action: 'Toggle#changeClass',
							toggle-target-value: '#overlay-newItemMarker',
							toggle-target-class-value: 'is-visible',
						],
					],
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove ImageList#removeImg'
						],
					]
				]
			]
		}
	</div>

{*	<div data-Templates-target="variantItem">*}
{*		{include $templates.'/part/box/list-item.latte',*}
{*		props: [*}
{*			dragdrop: true,*}
{*			data: [*}
{*				controller: 'ProductVariant RemoveItem',*}
{*				action: 'ProductVariantEdit:updateValues@window->ProductVariant#updateValues',*}
{*				removeitem-target: 'item',*}
{*				ProductVariant-id-value: 'newItemMarker',*}
{*			],*}
{*			texts: [*}
{*				[*}
{*					text: '<span data-ProductVariant-target="name">Nová varianta</span><span data-ProductVariant-target="price"></span>',*}
{*				]*}
{*			],*}
{*			btnsAfter: [*}
{*				[*}
{*					icon: $templates.'/part/icons/pencil-alt.svg',*}
{*					tooltip: 'Editovat',*}
{*					data: [*}
{*						controller: 'Toggle',*}
{*						action: 'Toggle#changeClass ProductVariant#edit',*}
{*						toggle-target-value: '#overlay-newItemMarker',*}
{*						toggle-target-class-value: 'is-visible'*}
{*					]*}
{*				],*}
{*				[*}
{*					icon: $templates.'/part/icons/trash.svg',*}
{*					tooltip: 'Odstranit',*}
{*					variant: 'remove',*}
{*					data: [*}
{*						action: 'RemoveItem#remove ProductVariant#remove',*}
{*					]*}
{*				]*}
{*			]*}
{*		]*}
{*		}*}
{*	</div>*}

{*	<div data-Templates-target="variantOverlay">*}
{*		{var $variantContainer = $form['variants']['newItemMarker']}*}

{*		{embed $templates.'/part/core/overlay.latte', props: [*}
{*			id: 'newItemMarker',*}
{*			title: 'Editace / Přidání varianty',*}
{*			data: [*}
{*				controller: 'ProductVariantEdit',*}
{*				ProductVariantEdit-id-value: 'newItemMarker',*}
{*			],*}
{*			classes: ['is-visible'],*}
{*		], templates=>$templates}*}
{*			{block content}*}
{*				{include './overlay/variant.latte', variantId=>'newItemMarker', variantContainer=>$variantContainer}*}
{*			{/block}*}
{*		{/embed}*}
{*	</div>*}

	<div data-Templates-target="imageOverlay">
		{var $imageContainer = $form['productCommon']->getComponent('images')['newItemMarker']}

		{embed $templates.'/part/core/overlay.latte', props: [
			id: 'newItemMarker',
			title: 'Editace obrázku',
		], templates=>$templates}
			{block content}
				{include './overlay/image.latte', imageId=>'newItemMarker', imageContainer=>$imageContainer}
			{/block}
		{/embed}
	</div>

	{var $productContainer = $form['productCommon']['products']['newItemMarker']}
	<div data-Templates-target="productItem">

		{var $url = $urls['searchProduct']->toArray()}
		{php $url['params']['ignoreSiblings'] = 1}

		{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $productContainer['name'],
						placeholder: 'Zadejte název produktu',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $productContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
				],
				dragdrop: true
			]
		}
	</div>

	{var $productTagParentContainer = $form['productCommon']['productTagParents']['newItemMarker']}
	<div data-Templates-target="productTagParentItem">

		{var $url = $urls['searchProductTagParent']->toArray()}

		{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $productTagParentContainer['name'],
						placeholder: 'Zadejte interní název příznaku',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $productTagParentContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
				],
				dragdrop: false
			]
		}
	</div>

	{*	<div data-Templates-target="linkItem">{% include '@components/templates/item-link.twig' %}</div>*}
	{*	<div data-Templates-target="pageItem">{% include '@components/templates/item-page.twig' %}</div>*}
</div>
