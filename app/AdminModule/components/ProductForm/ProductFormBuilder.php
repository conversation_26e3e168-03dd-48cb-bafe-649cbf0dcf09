<?php

namespace SuperKoderi\Admin\Components;

use App\Model\CatalogTreeModel;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\Parameter;
use App\Model\PriceLevel;
use App\Model\Product;
use App\Model\ProductLocalization;
use App\Model\ProductVariant;
use App\Model\ProductVariantModel;
use App\Model\State;
use App\Model\Stock;
use Nette\Application\UI\Form;
use Nette\Forms\Container;
use Nette\Utils\Arrays;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\Translator;

class ProductFormBuilder
{

	/** @var State[]|ICollection */
	private ICollection $states;

	/** @var PriceLevel[]|ICollection */
	private ICollection $priceLevels;

	/** @var Stock[]|ICollection */
	private ICollection $stocks;

	public function __construct(
		private Orm $orm,
		private CatalogTreeModel $catalogTreeModel,
		private Translator $translator,
	)
	{}


	/**
	 * @param ICollection|Mutation[] $mutations
	 */
	public function build(Form $form, Product $product, ICollection $mutations, array $postData): void
	{

		$this->states = $this->orm->state->findBy([
			'code' => 'CZ'
		]);
		$this->priceLevels = $this->orm->priceLevel->findAll();
		$this->stocks = $this->orm->stock->findAll();

		$this->addProductCommonToForm($form, $product, $mutations, $postData);
		$this->addProductLocalizationsToForm($form, $product, $mutations, $postData);
		$this->addVariantsToForm($form, $product, $mutations, $postData);

		$this->addButtonsCommon($form);
		$this->addButtonsMutation($form, $product, $mutations);
	}


	/**
	 * @param ICollection|Mutation[] $mutations
	 */
	private function addProductCommonToForm(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$container = $form->addContainer('productCommon');
		$container->addText('internalName', 'internalName')->setDefaultValue($product->internalName);
		$container->addCheckbox('isNew', $this->translator->translate('is_new'))->setDefaultValue($product->isNew);
		$container->addCheckbox('isSet', $this->translator->translate('is_set'))->setDefaultValue($product->isSet);
		$container->addCheckbox('isMikrosvin', $this->translator->translate('is_mikrosvin'))->setDefaultValue($product->isMikrosvin);
		$container->addCheckbox('isAction', $this->translator->translate('is_action'))->setDefaultValue($product->isAction);
		$container->addCheckbox('isIconic', $this->translator->translate('is_iconic'))->setDefaultValue($product->isIconic);
		$container->addCheckbox('isSale', $this->translator->translate('is_sale'))->setDefaultValue($product->isSale);
		$container->addText('publicFrom', 'publicFrom');
//		if ($product->publicFrom) {
			$container['publicFrom']->setDefaultValue($product->publicFrom->format('Y-m-d\TH:i'));
//		}
		$container->addText('publicTo', 'publicTo');
//		if ($product->publicTo) {
			$container['publicTo']->setDefaultValue($product->publicTo->format('Y-m-d\TH:i'));
//		}


		$container->addText('isNewExpirationTime', 'is_new_expiration_time');

		if (!$product->isNewExpirationTime) {
			$now = new DateTimeImmutable();
			$interval = new \DateInterval('P60D');
			$after60Days = $now->add($interval);
			$product->isNewExpirationTime = $after60Days;
		}

		$container['isNewExpirationTime']->setDefaultValue($product->isNewExpirationTime->format('Y-m-d\TH:i'));



		if ($product->isVoucher === 0) {
			$container->addSelect('template', 'template', ['Product:detail' => 'Product'])->setDefaultValue('Product:detail');
			$container->addInteger('storeLimit', 'storeLimit')->setDefaultValue($product->storeLimit);
		}

		$this->addAvailableServicesMultiselect($container, $product);
		$this->addImages($container, $product, $mutations, $postData);
		$this->addVats($container, $product);
		$this->addProductTagsParentToContainer($container, $product, $postData);

//		$this->addAccessories($container, $product, $postData);
		$this->addProducts($container, $product, $postData);
	}


	/**
	 * @param ICollection|Mutation[] $mutations
	 */
	private function addProductLocalizationsToForm(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$container = $form->addContainer('productLocalizations');
		foreach ($mutations as $mutation) {
			/** @var ProductLocalization $localization */
			$localization = $product->getLocalization($mutation);
			$localizationContainer = $container->addContainer($mutation->id);
			$localizationContainer->addText('name', $mutation->langCode . '_name')->setDefaultValue($localization->name);
			$localizationContainer->addText('nameTitle', $mutation->langCode . '_nameTitle')->setDefaultValue($localization->nameTitle);
			$localizationContainer->addText('nameAnchor', $mutation->langCode . '_nameAnchor')->setDefaultValue($localization->nameAnchor);

			$localizationContainer->addTextArea('annotation', $mutation->langCode . '_annotation')->setDefaultValue($localization->annotation);
			$localizationContainer->addTextArea('content', $mutation->langCode . '_content')->setDefaultValue($localization->content);
			$localizationContainer->addTextArea('description', $mutation->langCode . '_description')->setDefaultValue($localization->description);
			$localizationContainer->addTextArea('keywords', $mutation->langCode . '_keywords')->setDefaultValue($localization->keywords);

			$aliasString = ($localization->alias !== null) ? $localization->alias->alias : '';
			$localizationContainer->addText('alias', $mutation->langCode . '_alias')->setDefaultValue($aliasString);
			$localizationContainer->addTextArea('aliasHistory', $mutation->langCode . '_aliasHistory')->setDefaultValue($localization->getAliasHistoryString());


			$localizationContainer->addCheckbox('public', 'public')->setDefaultValue($localization->public);

			$this->addCategoriesToContainer($localizationContainer, $localization, $postData);
			$this->addPagesToContainer($localizationContainer, $localization, $postData);
			$this->addVouchersToContainer($localizationContainer, $localization, $postData);
			$this->addVideosToContainer($localizationContainer, $localization, $postData);
			$this->addLinksToContainer($localizationContainer, $localization, $postData);
			$this->addFilesToContainer($localizationContainer, $localization, $postData);

			$setupContainer = $localizationContainer->addContainer('setup');
			$setupContainer->addCheckbox('inheritCategories')->setDefaultValue(
				(isset($localization->setup->inheritCategories) && $localization->setup->inheritCategories)
			);
			$setupContainer->addCheckbox('inheritVideos')->setDefaultValue(
				(isset($localization->setup->inheritVideos) && $localization->setup->inheritVideos)
			);
			$setupContainer->addCheckbox('inheritFiles')->setDefaultValue(
				(isset($localization->setup->inheritFiles) && $localization->setup->inheritFiles)
			);
			$setupContainer->addCheckbox('inheritPages')->setDefaultValue(
				(isset($localization->setup->inheritPages) && $localization->setup->inheritPages)
			);
			$setupContainer->addCheckbox('inheritLinks')->setDefaultValue(
				(isset($localization->setup->inheritLinks) && $localization->setup->inheritLinks)
			);

		}
	}


	/**
	 * @param ICollection|Mutation[] $mutations
	 */
	private function addVariantsToForm(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$variantsContainer = $form->addContainer('variants');

		$parametersForVariant = $this->orm->parameter->findBy([
			'variantParameter' => 1
		]);

		if (!$postData) {
			foreach ($product->variants as $variant) {
				$this->addVariantToForm($variantsContainer, $variant, $variant->id, $parametersForVariant);
			}

		} else if (isset($postData['variants'])){
			foreach ($postData['variants'] as $variantKey=>$variantData) {
				if (preg_match('/^newItemMarker_/', $variantKey)) {
					$fakeVariant = null;
					$this->addVariantToForm($variantsContainer, $fakeVariant, $variantKey, $parametersForVariant);
				} else if (is_int($variantKey)) {
					$variant = $product->variants->toCollection()->getById($variantKey);
					$this->addVariantToForm($variantsContainer, $variant, $variantKey, $parametersForVariant);
				}
			}
		}


//		$fakeVariant = null;
//		$this->addVariantToForm($variantsContainer, $fakeVariant,  'newItemMarker', $parametersForVariant);

	}


	private function addVariantCommonToForm(Container $container, ProductVariant $variant = null, iterable $parametersForVariant): void
	{
		$container = $container->addContainer('variantCommon');

		$container->addText('ean', 'ean');
		$container->addText('code', 'code');

		if ($variant) {
			$container['ean']->setDefaultValue($variant->ean);
			$container['code']->setDefaultValue($variant->code);
		}

		foreach ($parametersForVariant as $key=>$parameter) {
			/** @var Parameter $parameter */
			$counter = $key + 1;
			$parameterValueFunctioName = "getterParam{$counter}ValueId";
			$container->addSelect("param{$counter}Value", $parameter->uid, $parameter->options->toCollection()->fetchPairs('id', 'internalValue'))
				->setPrompt('');

			if ($variant) {
				$container["param{$counter}Value"]->setDefaultValue($variant->$parameterValueFunctioName());
			}
		}

	}


	private function addVariantLocalizationsToForm(Container $container, ProductVariant $variant = null): void
	{
		$container = $container->addContainer('variantLocalizations');

		foreach ($this->orm->mutation->findAll() as $mutation) {
			$mutationsContainer = $container->addContainer($mutation->id);
			$mutationsContainer->addCheckbox('active', $mutation->langCode . '_active');

			if ($variant) {
				$variantLocalization = $variant->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
				$mutationsContainer['active']->setDefaultValue($variantLocalization->active);
			}
		}
	}


	private function addVariantPricesToForm(Container $container, ProductVariant $variant = null): void
	{
		$container = $container->addContainer('variantPrices');

		foreach ($this->orm->mutation->findAll() as $mutation) {
			$mutationsContainer = $container->addContainer($mutation->id);

			foreach ($this->priceLevels as $priceLevel) {
				$priceLevelContainer = $mutationsContainer->addContainer($priceLevel->id);
				$priceLevelContainer->addText('price', 'price');

				if ($variant && isset($variant->pricesByLevel[$mutation->id][$priceLevel->id])) {
					$priceLevelContainer['price']->setDefaultValue($variant->pricesByLevel[$mutation->id][$priceLevel->id]->price);
				}
			}
		}
	}

	private function addVariantSuppliesToForm(Container $container, ProductVariant $variant = null): void
	{
		$container = $container->addContainer('variantSupplies');
		foreach ($this->stocks as $stock) {
			$stockContainer = $container->addContainer($stock->id);
			$stockContainer->addText('amount', 'product_stock_amount');

			if ($variant && isset($variant->suppliesByStock[$stock->id])) {
				$stockContainer['amount']->setDefaultValue($variant->suppliesByStock[$stock->id]->amount);
			}
		}
	}


	/**
	 * @param ICollection|Mutation[] $mutations
	 */
	private function addButtonsMutation(Form $form, Product $product, ICollection $mutations): void
	{
		$container = $form->addContainer('buttonsMutation');

		foreach ($mutations as $mutation) {
			if ($localization = $product->getLocalization($mutation)) {
				$name = sprintf('%s%s', ProductForm::SUBMIT_MUTATION_REMOVE, Strings::firstUpper($mutation->langCode));
			} else {
				$name = sprintf('%s%s', ProductForm::SUBMIT_MUTATION_CREATE, Strings::firstUpper($mutation->langCode));
			}
			$container->addSubmit($name, $name);
		}
	}


	private function addButtonsCommon(Form $form): void
	{
		$form->addSubmit('send');
//		$form->addSubmit('delete');
	}


	private function addAvailableServicesMultiselect(Container $container, Product $product): void
	{
		$services = $this->orm->services->findAll()->fetchPairs('id', null);
		$services = array_map(function ($service) {
			$nameParts = [];
			$nameParts[] = $service->name;
			if (!$service->public) {
				$nameParts[] = '(nepublikovaná)';
			}
			return implode(' ', $nameParts);
		}, $services);
		$selectedValues = explode('|', $product->availableServices ?? '');
		foreach ($selectedValues as $key => $selectedValue) {
			if (!isset($services[$selectedValue])) {
				unset($selectedValues[$key]);
			}
		}
		$container->addMultiSelect('availableServices', 'Služby k dokoupení', $services)
			->setDefaultValue($selectedValues);
	}


	private function addVariantToForm(Container $variantsContainer, ProductVariant $variant = null, int|string $variantKey, ICollection $parametersForVariant): void
	{
		$variantContainer = $variantsContainer->addContainer($variantKey);
		$this->addVariantCommonToForm($variantContainer, $variant, $parametersForVariant);
		$this->addVariantLocalizationsToForm($variantContainer, $variant);
		$this->addVariantPricesToForm($variantContainer, $variant);
		$this->addVariantSuppliesToForm($variantContainer, $variant);
	}

	private function addVideosToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$videosContainer = $localizationContainer->addContainer('videos');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['videos'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['videos'] as $videoKey=>$video) {
					$videoContainer = $videosContainer->addContainer($videoKey);
					$videoContainer->addText('link', 'link')->setDefaultValue($video['link']);
					$videoContainer->addText('name', 'name')->setDefaultValue($video['name']);
				}
			}
		} else {
			foreach ($localization->videos as $videoKey=>$video) {
				$videoContainer = $videosContainer->addContainer($videoKey);
				$videoContainer->addText('link', 'link')->setDefaultValue($video->link);
				$videoContainer->addText('name', 'name')->setDefaultValue($video->name);
			}

		}

		//fake
		$videoContainer = $videosContainer->addContainer('newItemMarker');
		$videoContainer->addText('link', 'link')->setDefaultValue('');
		$videoContainer->addText('name', 'name')->setDefaultValue('');
	}



	private function addFilesToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$filesContainer = $localizationContainer->addContainer('files');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['files'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['files'] as $fileKey=>$file) {
					$fileContainer = $filesContainer->addContainer($fileKey);
					$fileContainer->addText('fileName', 'fileName')->setDefaultValue($file['fileName']);
					$fileContainer->addHidden('fileId', $file['fileId']);
				}
			}
		} else {
			foreach ($localization->files as $fileKey=>$productFile) {
				$fileContainer = $filesContainer->addContainer($fileKey);
				$fileContainer->addText('fileName', 'fileName')->setDefaultValue($productFile->name);
				$fileContainer->addHidden('fileId', $productFile->file->id);
			}

		}

		//fake
		$fileContainer = $filesContainer->addContainer('newItemMarker');
		$fileContainer->addText('fileName', 'fileName');
		$fileContainer->addHidden('fileId', '');

	}


	private function addLinksToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$linksContainer = $localizationContainer->addContainer('links');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['links'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['links'] as $linkKey=>$link) {
					$linkContainer = $linksContainer->addContainer($linkKey);
					$linkContainer->addText('link', 'link')->setDefaultValue($link['link']);
					$linkContainer->addText('name', 'name')->setDefaultValue($link['name']);
					$linkContainer->addCheckbox('open', 'open')->setDefaultValue(isset($link['open']));
				}
			}
		} else {
			foreach ($localization->links as $linkKey=>$link) {
				$linkContainer = $linksContainer->addContainer($linkKey);
				$linkContainer->addText('link', 'link')->setDefaultValue($link->link);
				$linkContainer->addText('name', 'name')->setDefaultValue($link->name);
				$linkContainer->addCheckbox('open', 'open')->setDefaultValue($link->open);

			}

		}

		//fake
		$linkContainer = $linksContainer->addContainer('newItemMarker');
		$linkContainer->addText('link', 'link')->setDefaultValue('');
		$linkContainer->addText('name', 'name')->setDefaultValue('');
		$linkContainer->addCheckbox('open', 'open')->setDefaultValue(0);
	}


	private function addPagesToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$pagesContainer = $localizationContainer->addContainer('pages');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['pages'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['pages'] as $pageKey=>$page) {
					$pageContainer = $pagesContainer->addContainer($pageKey);
					$pageContainer->addHidden('id');
					$pageContainer->addText('name', 'name');
				}
			}
		} else {
			foreach ($localization->pages as $pageKey=>$page) {
				$pageContainer = $pagesContainer->addContainer($pageKey);
				$pageContainer->addHidden('id')->setDefaultValue($page->id);
				$pageContainer->addText('name', 'name')
					->setDefaultValue($page->name);

			}
		}
		//fake
		$pageContainer = $pagesContainer->addContainer('newItemMarker');
		$pageContainer->addHidden('id');
		$pageContainer->addText('name', 'name');
	}


	private function addVouchersToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{

		$vouchersContainer = $localizationContainer->addContainer('vouchers');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['vouchers'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['vouchers'] as $pageKey=>$page) {
					$voucherContainer = $vouchersContainer->addContainer($pageKey);
					$voucherContainer->addHidden('id');
					$voucherContainer->addText('name', 'name');
				}
			}
		} else {
			if (($voucher = $localization->voucher) !== null) {
				$voucherKey = 0;
				$voucherContainer = $vouchersContainer->addContainer($voucherKey);
				$voucherContainer->addHidden('id')->setDefaultValue($voucher->id);
				$voucherContainer->addText('name', 'name')
					->setDefaultValue($voucher->internalName);

			}
		}
		//fake
		$voucherContainer = $vouchersContainer->addContainer('newItemMarker');
		$voucherContainer->addHidden('id');
		$voucherContainer->addText('name', 'name');
	}


	private function addProductRelation(string $containerName, ICollection $collection, Container $container, Product $product, array $postData): void
	{
		$itemsContainer = $container->addContainer($containerName);

		if ($postData) {
			if (isset($postData['productCommon'][$containerName])) {
				foreach ($postData['productCommon'][$containerName] as $itemKey=> $item) {
					$accessoryContainer = $itemsContainer->addContainer($itemKey);
					$accessoryContainer->addHidden('id');
					$accessoryContainer->addText('name');
				}
			}
		} else {
			foreach ($collection as $key=>$item) {
				$accessoryContainer = $itemsContainer->addContainer($key . '_'. $item->id);
				$accessoryContainer->addHidden('id')->setDefaultValue($item->id);
				$accessoryContainer->addText('name')->setDefaultValue($item->internalName);
			}
		}

		//fake
		$accessoryContainer = $itemsContainer->addContainer('newItemMarker');
		$accessoryContainer->addHidden('id');
		$accessoryContainer->addText('name');
	}

	private function addProductTagsParentToContainer(Container $container, Product $product, array $postData): void
	{
		$containerName = 'productTagParents';
		$collection = $product->productTagParents->toCollection();
		$this->addProductRelation($containerName, $collection, $container, $product, $postData);
	}

	private function addAccessories(Container $container, Product $product, array $postData): void
	{
		$containerName = 'accessories';
		$collection = $product->accessoriesAll;
		$this->addProductRelation($containerName, $collection, $container, $product, $postData);
	}


	private function addProducts(Container $container, Product $product, array $postData): void
	{
		$containerName = 'products';
		$collection = $product->productsAll;
		$this->addProductRelation($containerName, $collection, $container, $product, $postData);
	}


	private function addImages(Container $container, Product $product, ICollection $mutations, array $postData): void
	{
		/** @var  Mutation[]|ICollection $mutations */

		$containerName = 'images';
		$imagesContainer = $container->addContainer($containerName);


		if ($postData) {
			if (isset($postData['productCommon'][$containerName])) {
				foreach ($postData['productCommon'][$containerName] as $imageKey=>$item) {

					$imageContainer = $imagesContainer->addContainer($imageKey);
					$imageContainer->addHidden('imageId', $item['imageId']);
					$dataInp = [];
					foreach ($product->variants as $variant) {
						$dataInp[$variant->id] = 'Ean: '.$variant->ean.', '.$this->translator->translate('code').': '.$variant->code;
					}
					$imageContainer->addMultiSelect('variants','usedInVariants',$dataInp);
					foreach ($mutations as $mutation) {
						$imageNameContainer = $imageContainer->addContainer($mutation->langCode);
						$imageNameContainer->addText('name', $mutation->langCode . '_name');
						if (isset($item[$mutation->langCode]['name'])) {
							$imageNameContainer['name']->setDefaultValue($item[$mutation->langCode]['name']);
						}
					}
				}
			}
		} else {

			foreach ($product->images as $imageKey=>$productImage) {
				$imageContainer = $imagesContainer->addContainer($productImage->image);
				$imageContainer->addHidden('imageId', $productImage->image);
				$dataInp = [];
				foreach ($product->variants as $variant) {
					$dataInp[$variant->id] = 'Ean: '.$variant->ean.', '.$this->translator->translate('code').': '.$variant->code;
				}
				$imageContainer->addMultiSelect('variants','usedInVariants',$dataInp);
				if (!empty($productImage->getVariantIds()) && isset($productImage->getVariantIds()[0]) && $productImage->getVariantIds()[0]!='') {
					$imageContainer['variants']->setDefaultValue($productImage->getVariantIds());
				}
				foreach ($mutations as $mutation) {

					$imageNameContainer = $imageContainer->addContainer($mutation->langCode);
					$imageNameContainer->addText('name', $mutation->langCode . '_name');

					$langCode = $mutation->langCode;
					if (isset($productImage->data->$langCode->name)) {
						$imageNameContainer['name']->setDefaultValue($productImage->data->$langCode->name);
					}
				}
			}
		}

		// fake
		$imageContainer = $imagesContainer->addContainer('newItemMarker');
		$imageContainer->addHidden('imageId', '');
		$dataInp = [];
		foreach ($product->variants as $variant) {
			$dataInp[$variant->id] = 'Ean: '.$variant->ean.', '.$this->translator->translate('code').': '.$variant->code;
		}
		$imageContainer->addMultiSelect('variants','usedInVariants',$dataInp);
		foreach ($mutations as $mutation) {
			$imageNameContainer = $imageContainer->addContainer($mutation->langCode);
			$imageNameContainer->addText('name', $mutation->langCode . '_name');
			$langCode = $mutation->langCode;
//			if (isset($image->data->$langCode->name)) {
//				$imageNameContainer['name']->setDefaultValue($image->data->$langCode->name);
//			}
		}

	}


	private function addVats(Container $container, Product $product): void
	{
		$containerName = 'vats';
		$vatsContainer = $container->addContainer($containerName);

		foreach ($this->states as $state) {
			$stateContainer = $vatsContainer->addContainer($state->id);

			$vatRates = Arrays::map((array)$state->vatRates, function ($v, $k) {
				return sprintf('%d%% (%s)', $v, $this->translator->translate('vat_rate_' . $k));
			});

			$stateId = $state->id;

			$stateContainer->addSelect('vatRate', $state->code, $vatRates)->setTranslator(null);
			if (isset($product->vats->$stateId)) {
				$stateContainer['vatRate']->setDefaultValue($product->vats->$stateId);
			}
		}
	}


	private function addCategoriesToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$categoriesContainer = $localizationContainer->addContainer('categories');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['categories'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['categories'] as $categoryKey=>$category) {
					$categoryContainer = $categoriesContainer->addContainer($categoryKey);
					$categoryContainer->addHidden('id');
					$categoryContainer->addText('name', 'name');
				}
			}
		} else {


			$categories = $this->catalogTreeModel->findCategoriesForProductLocalization($localization);

			foreach ($categories as $categoryKey=>$category) {
				$categoryContainer = $categoriesContainer->addContainer($categoryKey);
				$categoryContainer->addHidden('id', $category->id);
				$categoryContainer->addText('name', 'name')
					->setDefaultValue($category->getPathSentenceWithMyself());

			}
		}

		//fake
		$categoryContainer = $categoriesContainer->addContainer('newItemMarker');
		$categoryContainer->addHidden('id');
		$categoryContainer->addText('name', 'name');
	}

}
