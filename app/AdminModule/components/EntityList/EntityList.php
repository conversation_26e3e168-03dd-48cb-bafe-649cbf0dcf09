<?php declare(strict_types=1);

namespace SuperKoderi\Components;

use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Paginator;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\Translator;

/**
 * @property-read DefaultTemplate $template
 */
final class EntityList extends UI\Control
{

	private Paginator $paginator;

	public function __construct(
		private readonly ICollection $collection,
		private readonly Translator $translator,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
	)
	{
	}

	private function initRender(): void
	{
		$visualPaginator = $this['pager'];
		$visualPaginator->setTranslator($this->translator);
		$this->paginator = $visualPaginator->getPaginator();
		$this->paginator->itemsPerPage = 20;
		$this->template->setTranslator($this->translator);
	}

	public function renderApplications(): void
	{
		$this->initRender();
//		$this->paginator->itemsPerPage = 2;

		$this->paginator->itemCount = $this->collection->countStored();
		$this->template->items = $this->collection->limitBy($this->paginator->itemsPerPage, $this->paginator->offset);
		$this->template->vp = $this->paginator;

		$this->template->render(__DIR__ . '/application.latte');
	}

	public function renderRent(): void
	{
		$this->initRender();
//		$this->paginator->itemsPerPage = 2;

		$this->paginator->itemCount = $this->collection->countStored();
		$this->template->items = $this->collection->limitBy($this->paginator->itemsPerPage, $this->paginator->offset)->orderBy(['id' => 'DESC']);
		$this->template->vp = $this->paginator;

		$this->template->render(__DIR__ . '/rent.latte');
	}

	public function renderGroups(): void
	{
		$this->initRender();
//		$this->paginator->itemsPerPage = 2;

		$this->paginator->itemCount = $this->collection->countStored();
		$this->template->items = $this->collection->limitBy($this->paginator->itemsPerPage, $this->paginator->offset);
		$this->template->vp = $this->paginator;

		$this->template->render(__DIR__ . '/groups.latte');
	}

    public function renderRedirects(): void
    {
        $this->initRender();

        $this->paginator->itemCount = $this->collection->countStored();

//        $this->paginator->itemsPerPage = 2;

        $this->template->items = $this->collection->limitBy($this->paginator->itemsPerPage, $this->paginator->offset);
        $this->template->vp = $this->paginator;

        $this->template->render(__DIR__ . '/redirects.latte');
    }

	public function render(): void
	{
		$this->initRender();
		$this->paginator->itemCount = $this->collection->countStored();
		$this->template->items = $this->collection->limitBy($this->paginator->itemsPerPage, $this->paginator->offset);;
		$this->template->vp = $this->paginator;

		$this->template->render(__DIR__ . '/list.latte');
	}

	protected function createComponentPager(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}

}


interface IEntityListFactory
{
	public function create(ICollection $collection): EntityList;
}
