<?php declare(strict_types = 1);

namespace App\Admin\Components;

use App\Model\Mutation;
use App\Model\Orm;
use App\Model\UserHash;
use App\Model\UserHashModel;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\Translator;

/**
 * @property-read DefaultTemplate $template
 */
class LostPasswordForm extends UI\Control
{

	use hasMessageForFormComponentTrait;
	use hasConfigServiceTrait;

	private Translator $translator;

	private Orm $orm;

	private UserHashModel $userHashModel;

	private ICommonFactory $commonEmailFactory;

	private Mutation $mutation;

	public function __construct(
		Mutation $mutation,
		Orm $orm,
		Translator $translator,
		UserHashModel $userHashModel,
		ICommonFactory $commonEmailFactory
	)
	{
		$this->translator = $translator;
		$this->orm = $orm;
		$this->userHashModel = $userHashModel;
		$this->commonEmailFactory = $commonEmailFactory;
		$this->mutation = $mutation;
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/lostPasswordForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'email')
			->setRequired('form_enter_email');

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'lostPasswordFormSucceeded'];
		return $form;
	}


	public function lostPasswordFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->orm->user->getByEmail($values->email, $this->mutation);
		if ($user) {
			$userHash = $this->userHashModel->generateHashForUser($user, UserHash::TYPE_LOST_PASSWORD, ['email' => $user->email], 1);
			$domainClear = $this->mutation->getBaseUrl();

			$mailData = [];

			$mailData['link'] = $domainClear . '/' . $this->configService->get('adminAlias') . '/reset-hesla?hashToken=' . $userHash->hash;

			$this->commonEmailFactory
				->create()
				->send('', $user->email, 'lostPassword', $mailData);

			$this->flashMessage('form_send_reset_password', 'ok');
			$this->redirect('this');
		} else {
			$this->flashMessage('User not found', 'error');
		}
	}

}


interface ILostPasswordFormFactory
{

	public function create(Mutation $mutation): LostPasswordForm;

}
