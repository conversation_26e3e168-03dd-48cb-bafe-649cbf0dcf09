<?php

declare(strict_types=1);

namespace App\Admin\Components\SeoLinkForm;

use App\Admin\Components\SeoLinkForm\FormData\BaseFormData;
use App\Model\Orm;
use App\Model\Routable;
use App\Model\SeoLink;
use App\Model\SeoLinkLocalization;
use App\Model\User;
use App\PostType\Core\AdminModule\Components\Form\Handler as CoreHandler;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly Orm $orm,
		private readonly CoreHandler $coreHandler,
	)
	{
	}

	public function handle(SeoLinkLocalization $seoLinkLocalization, User $user, BaseFormData $data, ArrayHash $postData): void
	{
		$this->handleCategories($seoLinkLocalization, $data);
		$this->coreHandler->handleParent($seoLinkLocalization->seoLink, $data->parent);
		$this->coreHandler->handleLocalization($seoLinkLocalization, $data->localization);
		if ($seoLinkLocalization instanceof Routable) {
			$this->coreHandler->handleRoutable($seoLinkLocalization, $data->routable);
		}

		$this->handleParameterValues($seoLinkLocalization->seoLink, $postData);

		$this->orm->seoLinkLocalization->persistAndFlush($seoLinkLocalization);
	}

	private function handleParameterValues(SeoLink $seoLink, ArrayHash $values): void
	{
		$parameterValues = [];
		if (isset($values->parameterValues)) {
			foreach ($values->parameterValues as $parameterValueRow) {
				$parameterValue = $this->orm->parameterValue->getById($parameterValueRow->id);
				if ($parameterValue !== null) {
					$parameterValues[$parameterValueRow->id] = $parameterValue;
				}
			}
		}

		$seoLink->parameterValues->set($parameterValues);
	}

	private function handleCategories(SeoLinkLocalization $seoLinkLocalization, BaseFormData $data): void
	{

		$categoryEntity = $seoLinkLocalization->getMutation()->pages->eshop;
		if (isset($data->seoLinkLocalization->categories)) {
			foreach ($data->seoLinkLocalization->categories as $category) {
				if (isset($category->id)) {
					$categoryEntity = $this->orm->tree->getById($category->id);
					if ($categoryEntity !== null) {
						break;
					}
				}

			}
		}
		$seoLinkLocalization->category = $categoryEntity;
	}

}
