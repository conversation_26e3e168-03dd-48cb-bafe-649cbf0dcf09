<?php

namespace AdminModule;

use Nette\Application\Request;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\Expression\LikeExpression;

/**
 * @property-read DefaultTemplate $template
 * @property-read Request|null $request
 */
class Search2Presenter extends BasePresenter
{

	public function actionParameter(string $types = '', bool $onlyForFilter = false): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search) {
			$parameters = $this->orm->parameter->searchByName($search, $selectedSiblingsIds);
			if ($types) {
				$types = explode(',', $types);
				$parameters = $parameters->findBy([
					'type' => $types
				]);
			}
			if ($onlyForFilter) {
				$parameters = $parameters->findBy([
					'isInFilter' => 1
				]);
			}


			$this->template->parameters = $parameters;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionSeoLinkParameterValues(): void
	{
		$searchQuery = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($searchQuery !== '') {
			$allowedParamUids = $this->configService->get('seoLink', 'paramsList');
			$conditions = [
				'internalValue~' => LikeExpression::contains($searchQuery),
//				'parameter->isInFilter' => 1,
				'parameter->uid' => $allowedParamUids,
			];

			if ($selectedSiblingsIds !== []) {
				$conditions['id!='] = $selectedSiblingsIds;
			}

			$parameterValues = $this->orm->parameterValue->findBy($conditions);
			$this->template->parameterValues = $parameterValues;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionVoucher(?int $mutationId = null): void
	{
		$searchQuery = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($searchQuery !== '') {
			$conditions = [
				'internalName~' => LikeExpression::contains($searchQuery),
			];

			if ($selectedSiblingsIds !== []) {
				$conditions['id!='] = $selectedSiblingsIds;
			}

			$vouchers = $this->orm->voucher->findBy($conditions);
			$this->template->vouchers = $vouchers;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionSeoLink(?int $mutationId = null): void
	{
		$searchQuery = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($searchQuery !== '') {
			$conditions = [
				'name~' => LikeExpression::contains($searchQuery),
			];
			if ($mutationId) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation) {
					$conditions['mutation'] = $mutationId;
				}
			}

			if ($selectedSiblingsIds !== []) {
				$conditions['id!='] = $selectedSiblingsIds;
			}

			$seoLinks = $this->orm->seoLinkLocalization->findBy($conditions);
			$this->template->seoLinks = $seoLinks;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionPage(?int $parentId = null, ?int $pathId = null): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search) {

			$pages = $this->orm->tree->searchByName($search, $parentId, $pathId, $selectedSiblingsIds);
			$this->template->result = $pages;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionProduct(int $ignoreSiblings = 0): void
	{
		$search = trim($this->request->getPost('search') ?? '');


		if ($ignoreSiblings === 1) {
			$selectedSiblingsIds = [];
		} else {
			$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
			$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);
		}



		if ($search) {
			$excluded = null;

			$productsByBatch = $this->orm->product->findByParameterValue("batchNumber", $search)->fetchPairs("id", "name");
			$productsByYear = $this->orm->product->findByParameterValue("year", $search)->fetchPairs("id", "name");
			$productsByName = $this->orm->product->searchByName($search, $selectedSiblingsIds)->fetchPairs("id", "name");

			$productsIds = array_merge(array_keys($productsByBatch), array_keys($productsByYear), array_keys($productsByName));

			$products = $this->orm->product->findBy(["id" => $productsIds]);
			$this->template->result = $products;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionProductTagParent(): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		if ($search) {
			$this->template->results = $this->orm->productTagParent->findByName($search);
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionPageInMutation(?int $parentId = null, ?int $pathId = null, ?int $mutationId = null, string $templates = ''): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search) {

			$pages = $this->orm->tree->searchByName($search, $parentId, $pathId, $selectedSiblingsIds);
			if ($mutationId) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation) {

					$pages = $pages->findBy([
						'rootId' => $mutation->rootId
					]);
				}

				if ($templates !== '') {
					$pages = $pages->findBy(['template' => explode(',', $templates)]);
				}
			}
			$this->template->result = $pages;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionBlogInMutation(?int $mutationId = null): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search) {

			$blogs = $this->orm->blog->searchByName($search, $selectedSiblingsIds);
			if ($mutationId) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation) {
					$blogs = $blogs->findBy([
						'mutation' => $mutation
					]);
				}
			}
			$this->template->result = $blogs;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionParameterValue(string $uids = ''): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search) {

			$parametersValues = $this->orm->parameterValue->searchByInternalValue($search);

			if ($selectedSiblingsIds !== []) {
				$parametersValues = $parametersValues->findBy(['id!=' => $selectedSiblingsIds]);
			}

			if ($uids !== '') {
				$uids = explode(',', $uids);
				$parametersValues = $parametersValues->findBy(['parameter->uid' => $uids]);
			}

			$this->template->parametersValues = $parametersValues;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionVariant(): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search !== '') {

			$products = $this->orm->product->searchByName($search, );

			if ($selectedSiblingsIds !== []) {
				$products = $products->findBy(['variants->id!=' => $selectedSiblingsIds]);
			}

			bd($products);
			$this->template->products = $products;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionAlias(?int $mutationId = null): void
	{
		$search = trim($this->request->getPost('search') ?? '');
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);

		if ($search !== '') {

			$aliases = $this->orm->alias->findByName($search);

			if ($selectedSiblingsIds !== []) {
				$aliases = $aliases->findBy(['id!=' => $selectedSiblingsIds]);
			}

			if ($mutationId !== null) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation !== null) {
					$aliases = $aliases->findBy([
						'mutation' => $mutation,
					]);
				}
			}

			$this->template->aliases = $aliases->limitBy(20);
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	private function parseSiblingsIds(string $selectedSiblingsIds): array
	{
		if ($selectedSiblingsIds) {
			return explode('|', $selectedSiblingsIds);
		} else {
			return [];
		}
	}


	private function getSearch(): string
	{
		$search = trim($this->request->getPost('search') ?? '');
		if ($search === '') {
			$search = trim($this->request->getParameter('search') ?? '');
		}

		return $search;
	}


	private function getSelectedSiblingsIds(): array
	{
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);
		return $selectedSiblingsIds;
	}


}
