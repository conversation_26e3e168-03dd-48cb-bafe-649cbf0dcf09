<?php declare(strict_types = 1);

namespace AdminModule;

use App\Model\FileModel;
use Nette\DI\Attributes\Inject;
use Nette\Http\FileUpload;
use Nette\Utils\Json;

class FilePresenter extends BasePresenter
{

	#[Inject]
	public FileModel $fileModel;


	public function actionUpload(): void
	{
		if (isset($_FILES['file'])) {
			$file = new FileUpload($_FILES['file']);
			$fileEntity = $this->fileModel->add($file);
			echo Json::encode([
				'id' => $fileEntity->id,
			]);
		}

		$this->terminate();
	}

}
