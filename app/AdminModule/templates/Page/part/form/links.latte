{if !isset($k)} {var $k='XXX'} {/if}
{if !isset($data)} {var $data=array('url'=>'http://', 'name'=>'', 'open' => 0)} {/if}
<li>
	<div class="inner">
		<span class="drag-area js-handle"></span>
		<div class="grid-row">
			<p class="grid-2-5">
				<span class="inp-fix">
					<input type="text" class="inp-text w-full" name="linkUrl[{$k}]" id="inp-video{$k}" value="{$data['url']}" data-val="http://" />
				</span>
			</p>
			<p class="grid-2-5">
				<span class="inp-fix">
					<input type="text" class="inp-text w-full" name="linkName[{$k}]" id="inp-video{$k}" value="{$data['name']}" />
				</span>
			</p>
			<p class="grid-1-5 center">
				<span class="inp-item inp-center">
					<input type="checkbox" name="linkOpen[{$k}]" value="1" id="inp-new-window{$k}" {if $data['open']}checked="checked"{/if}/>
					<label for="inp-new-window{$k}">{_Yes}</label>
				</span>
			</p>
		</div>
		<input type="hidden" name="linkSort[{$k}]" value="{$k}" class="inp-sort" />
		<a href="#" class="icon icon-close remove"></a>
	</div>
</li>