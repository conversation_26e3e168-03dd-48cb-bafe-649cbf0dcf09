{default $skip = []}
{foreach $menu as $t}

	{continueIf !$t || (isset($t->id) && in_array($t->id, $skip))}

	{if isset($t->id)}
		<li id="t[{$t->id}]" n:href="Page:default $t->id" n:class="in_array($t->id, $active)?jstree-checked">
			<a n:href="Page:default $t->id" {if in_array($t->id, $active)}class="jstree-checked"{/if}>{$t->name}</a>
			{if !empty($t->crossroad)}
				<ul>
					{include 'menu.latte', 'menu' => $t->crossroad, 'active' => $active}
				</ul>
			{/if}
		</li>
	{/if}
{/foreach}
