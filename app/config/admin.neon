parameters:
	config:
		adminMenu:
			Modules:
				- {title: Library, resource: Admin:Library, action: Library:default, icon: file}
				- {title: Pages, resource: Admin:Page, action: Page:default, icon: file}
				- {title: Products, resource: Admin:Catalog, action: Catalog:default, icon: file}
				- {title: ProductTag, resource: Admin:ProductTag, action: ProductTag:default, icon: tag, sub: true}
#				- {title: Discounts, resource: Admin:Discount, action: Discount:default, icon: file, sub: true}
				- {title: Blog, resource: Admin:Blog, action: Blog:default, icon: file}
				- {title: SEO filtry, resource: Admin:SeoLink, action: SeoLink:default, icon: file}
				- {title: Holidays, resource: Admin:Holiday, action: Holiday:default, icon: file}

			Lists: # veci které se plní frontenedem
				- {title: Orders, resource: Admin:Order, action: Order:default, icon: file}
				- {title: E-mails, resource: Admin:Newsletter, action: Newsletter:emails, icon: file}
#				- {title: Reviews, resource: Admin:Review, action: Review:default, icon: file}

			Settings:
				- {title: System strings, resource: Admin:String, action: String:default, icon: file}
				- {title: Mutations, resource: Admin:Mutation, action: Mutation:default, icon: file}
#				- {title: States, resource: Admin:State, action: State:default, icon: file}
				- {title: Transports, resource: Admin:Transport, action: Transport:default, icon: file}
				- {title: Users, resource: Admin:User, action: User:default, icon: file}
				- {title: Parameters, resource: Admin:Parameter, action: Parameter:default, icon: file}
#				- {title: Places, resource: Admin:Place, action: Place:default, icon: file}
				- {title: E-mails template, resource: Admin:Email, action: Email:default, icon: file}
				- {title: Redirects, resource: Admin:Redirect, action: Redirect:default, icon: file}
				- {title: Vouchers, resource: Admin:Voucher, action: Voucher:default, icon: file}

			-
#				- {title: Šablony, resource: Admin:Template, action: Template:default, icon: file}
				- {title: Elastic search, resource: Admin:Elastic, action: Elastic:default, icon: file, devOnly: true} #vidi jen developeri
				- {title: Help, resource: superadmin, action: Help:default, icon: file}
				- {title: About, resource: superadmin, action: Help:about, icon: file}

			-
				- {title: Styleguide, resource: Admin:Styleguide, action: Styleguide:default, icon: file, devOnly: true}


		modules:
			Admin:Homepage: Homepage
			Admin:SeoLink: SeoLink

			Admin:Page: Pages
			Admin:Blog: Blog
			Admin:Discount: Discount
			Admin:Library: Library
			Admin:Library2: Library
			Admin:File: File
			Admin:Catalog: Catalog
			Admin:Product: Products
			Admin:Order: Orders
			Admin:User: Users
			Admin:Parameter: Parameters
			Admin:Voucher: Vouchers
			Admin:Email: E-mails
			Admin:String: System strings
			Admin:Developer: Developerské nastavení
			Admin:Help: Help
			Admin:Newsletter: Newsletter
			Admin:Place: Place
			Admin:Search: Search
			Admin:Search2: Search
			Admin:Review: Reviews
			Admin:Reservation: Reservations
			Admin:Template: Templates
			Admin:Group: Group
			Admin:Redirect: Redirect
			Admin:Mutation: Mutation
			Admin:Elastic: Elastic
			Admin:Holiday: Holiday
			Admin:State: State
			Admin:Transport: Transport
			Admin:Styleguide: Styleguide

		adminPaging: 20
		productPaging: 20
		adminImagePaging: 30

		tabs:
			pages: [
				images,
				videos,
				files,
#				links,
				params,
#				faqs,
#				attproducts, #pripojene produkty ke strance
#				attproductsReview, #pripojene produkty pro ktere je clanek recenze
#				pages,
#				products, # razeni produktu na "produktové kategorii"
				linkedCategories,
				seo,
				settings,
			]
			products: [
				variants,
				content,
				images,
				files,
				params,
				links,
				videos,
				reviews,
#				sets,
#				pages,
				articlePages,
				accessories, # doporucujeme
				presents,
				similar, #podobne produkty
				product, #podobne produkty pro vyprodany produkt
				contents,
				seo,
				settings,
			]
			emailTemplates: [
				files,
			]

		tabsHideOnlyOnTemplates:
			pages:
#				files:
#				links:
#				videos:
				attproducts:
#					- Article:detail
#					- Article:default
				pages:
#					- Article:detail
#					- Article:default
				faqs:
#					- Article:detail
#					- Article:default
				reviews:
#					- Article:detail
#					- Article:default
				attproductsReview:
#					- Article:detail
#					- Article:default



		tabsShowOnlyOnTemplates: # note: tabs are defined in admin.neon
			pages:
				params:
#					- Article:detail
				linkedCategories:
					- Catalog:default
#				faqs:
#				reviews:

includes:
	- lang/lang.neon
