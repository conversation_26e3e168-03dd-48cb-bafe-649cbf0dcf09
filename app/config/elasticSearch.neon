parameters:

	esBaseName: %config.projectName%_%stageName%

	config:
		elasticSearch:
			enabled: true
			pathToSynonyms: %config.APP_DIR%/../documents/es/synonyms/

			index:
				tree:
					name: '%esBaseName%_tree'
					enabled: true
				product:
					name: '%esBaseName%_product'
					enabled: true
				variant:
					name: '%esBaseName%_variant'
					enabled: true
				all:
					name: '%esBaseName%_all'
					enabled: true
				common:
					name: '%esBaseName%_common'
					enabled: true


		bucketFilter:
			flags: []
			flagValues: []
			ranges: ['price']

		elastica:
			debug: %debugMode%
			config:
				host: localhost
				port: 9200


services:
	elastica.client: Contributte\Elastica\Client(%config.elastica.config%) # turn off default logger

	BucketFilter:
		implement: SuperKoderi\BucketFilter\IBucketFilterFactory
		inject: true
	QueryAggregation:
		implement: SuperKoderi\BucketFilter\IQueryAggregationFactory
	QueryFilter:
		implement: SuperKoderi\BucketFilter\IQueryFilterFactory

	- SuperKoderi\BucketFilter\CatalogParameter
	- SuperKoderi\BucketFilter\QueryBaseFilter()
	- SuperKoderi\BucketFilter\IndexMapper()
	- SuperKoderi\BucketFilter\FilterResultMapper
	- App\Model\ElasticSearch\ConfigurationHelper
	- App\Model\ElasticSearch\Logger
	- SuperKoderi\BucketFilter\SortCreator




	- App\Model\EsIndexModel
	- App\Model\EsIndexFacade

	- \App\Model\ElasticSearch\IndexModel(%esBaseName%)
	- \App\Model\ElasticSearch\Repository()

	- \App\Model\ElasticSearch\Product\Repository
	- \App\Model\ElasticSearch\All\Repository
	- \App\Model\ElasticSearch\Common\Repository

	- \App\Model\ElasticSearch\ClientFactory(%config.elastica.config.host%, %config.elastica.config.port%)


## new version
	- App\Model\ElasticSearch\Service


	#index of PRODUCT
	- App\Model\ElasticSearch\Product\ConvertorProvider
	- App\Model\ElasticSearch\Product\Facade
	- App\Model\ElasticSearch\Product\ResultReader

	- App\Model\ElasticSearch\Product\Convertor\BaseData
	- App\Model\ElasticSearch\Product\Convertor\CategoryData
	- App\Model\ElasticSearch\Product\Convertor\ParameterData
	- App\Model\ElasticSearch\Product\Convertor\StoreData
	- App\Model\ElasticSearch\Product\Convertor\PriceData
	- App\Model\ElasticSearch\Product\Convertor\TopScoreData
	- App\Model\ElasticSearch\Product\Convertor\SoldCountData



	# index of ALL
	- App\Model\ElasticSearch\All\Facade
	- App\Model\ElasticSearch\All\ResultReader

	- App\Model\ElasticSearch\All\ConvertorProvider
	- App\Model\ElasticSearch\All\Convertor\ProductData
	- App\Model\ElasticSearch\All\Convertor\TreeData
	- App\Model\ElasticSearch\All\Convertor\BlogData
	- App\Model\ElasticSearch\All\Convertor\SeoLinkData

	# index of COMMON
	- App\Model\ElasticSearch\Common\Facade
	- App\Model\ElasticSearch\Common\ResultReader
	- App\Model\ElasticSearch\Common\ConvertorProvider

	- App\Model\ElasticSearch\Common\Convertor\TreeData
	- App\Model\ElasticSearch\Common\Convertor\BlogData



	- App\Model\BucketFilter\Parts\Flag
	- App\Model\BucketFilter\Parts\FlagValues
	- App\Model\BucketFilter\Parts\Dial
	- App\Model\BucketFilter\Parts\MainCategory
	- App\Model\BucketFilter\Parts\Range

	- App\Model\ElasticSearch\Product\ProductGroup


extensions:
	elastica: Contributte\Elastica\DI\ElasticaExtension
