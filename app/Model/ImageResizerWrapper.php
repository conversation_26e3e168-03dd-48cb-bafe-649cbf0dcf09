<?php

namespace SuperKoderi;

use App\Model\ImageEntity;

class ImageResizerWrapper
{

	public function __construct(
		private ImageResizer $imageResizer,
	) {}

	public function getResizedImage(object $object, string $size): ?\stdClass
	{
		if ($object instanceof ImageEntity) {
			return $this->imageResizer->getImg($object->filename, $size);
		}

		if (isset($object->firstImage)) {
			return $this->imageResizer->getImg($object->firstImage->filename, $size);
		}

		return null;
	}

}
