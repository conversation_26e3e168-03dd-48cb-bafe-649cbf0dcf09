<?php declare(strict_types = 1);

namespace App\Model;

use LogicException;
use Nextras\Orm\Model\Model;

/**
 * @property-read TreeRepository $tree
 * @property-read AliasRepository $alias
 * @property-read AttachedFileRepository $attachedFile
 * @property-read TreeImageRepository $treeImage
 * @property-read TreeFileRepository $treeFile
 * @property-read OrderRepository $order
 * @property-read OrderItemRepository $orderItem
 * @property-read ProductRepository $product
 * @property-read ProductCommentRepository $productComment
 * @property-read ProductReviewRepository $productReview
 * @property-read ProductVariantRepository $productVariant
 * @property-read ProductVariantPriceRepository $productVariantPrice
 * @property-read ProductImageRepository $productImage
 * @property-read StockRepository $stock
 * @property-read SupplyRepository $supply
 * @property-read LibraryTreeRepository $libraryTree
 * @property-read LibraryImageRepository $libraryImage
 * @property-read ParameterRepository $parameter
 * @property-read ParameterValueRepository $parameterValue
 * @property-read UserRepository $user
 * @property-read UserImageRepository $userImage
 * @property-read ProductFileRepository $productFile
 * @property-read ProductTreeRepository $productTree
 * @property-read UserHashRepository $userHash
 * @property-read AliasHistoryRepository $aliasHistory
 * @property-read ParameterValueImageRepository $parameterValueImage
 * @property-read ImageRepository $image
 * @property-read ProductContentRepository $productContent
 * @property-read BannerRepository $banner
 * @property-read BannerImageRepository $bannerImage
 * @property-read VoucherCodeRepository $voucherCode
 * @property-read VoucherRepository $voucher
 * @property-read ProductParameterIconRepository $paramsIcon
 * @property-read BasketItemRepository $basketItem
 * @property-read ServicesRepository $services
 * @property-read TreeProductRepository $treeProduct
 * @property-read GroupProductRepository $groupProduct
 * @property-read GroupRepository $group
 * @property-read SeoLinkRepository $seoLink
 * @property-read SeoLinkLocalizationRepository $seoLinkLocalization
 * @property-read RedirectRepository $redirect
 * @property-read FileRepository $file
 * @property-read PlaceRepository $place
 * @property-read MutationRepository $mutation
 * @property-read EmailTemplateRepository $emailTemplate
 * @property-read EmailTemplateFileRepository $emailTemplateFile
 * @property-read EsIndexRepository $esIndex
 * @property-read MutationTransportsRepository $mutationTransports
 * @property-read MutationPaymentsRepository $mutationPayments
 * @property-read ProductLocalizationRepository $productLocalization
 * @property-read ProductVariantLocalizationRepository $variantLocalization
 * @property-read ProductProductRepository $productProduct
 * @property-read TreeTreeRepository $treeTree
 * @property-read HolidayRepository $holiday
 * @property-read StateRepository $state
 * @property-read PriceLevelRepository $priceLevel
 * @property-read BlogRepository $blog
 * @property-read DiscountRepository $discount
 * @property-read DiscountPriceRepository $discountPrice
 * @property-read UserMutationRepository $userMutation
 * @property-read NewsletterEmailRepository $newsletterEmail
 * @property-read ApiUserRepository $apiUser
 * @property-read ImportCacheRepository $importCache
 * @property-read StringRepository $string
 * @property-read ConfigRepository $config
 * @property-read UserFavoriteProductsRepository $userFavoriteProducts
 * @property-read ProductTagRepository $productTag
 * @property-read ProductTagParentRepository $productTagParent
 */
class Orm extends Model
{

	private ?Mutation $selectedMutation = null;

	private bool $globalPublicOnly = true;

	public function setMutation(Mutation $mutation): void
	{
		$this->selectedMutation = $mutation;
	}


	public function getMutation(): Mutation
	{
		if ($this->selectedMutation === null) {
			throw new LogicException('ORM mutation setup missing.');
		}

		return $this->selectedMutation;
	}


	public function hasMutation(): bool
	{
		return $this->selectedMutation !== null;
	}


	public function setPublicOnly(bool $globalPublicOnly = true): void
	{
		$this->globalPublicOnly = $globalPublicOnly;
	}


	public function getPublicOnly(): bool
	{
		return $this->globalPublicOnly;
	}

}
