<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasCacheTrait;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasConstsTrait;
use SuperKoderi\hasFormDefaultDataTrait;

/**
 * @property int $id {primary}
 * @property string $status {enum self::STATUS_*} {default self::STATUS_NEW}
 * @property string|null $transportType {enum self::TRANSPORT_*} {default self::TRANSPORT_PERSONAL}
 * @property string|null $paymentType {enum self::PAYMENT_*} {default self::PAYMENT_CASH}
 * @property string $number {default ''}
 * @property DateTimeImmutable $created {default 'now'}
 * @property string|null $transportName
 * @property string $email {default ''}
 * @property string $firstname {default ''}
 * @property string $lastname {default ''}
 * @property string $phone {default ''}
 * @property string $street {default ''}
 * @property string $city {default ''}
 * @property string $zip {default ''}
 * @property string $company {default ''}
 * @property string $ic {default ''}
 * @property string $dic {default ''}
 * @property string $dFirstname {default ''}
 * @property string $dLastname {default ''}
 * @property string $dCompany {default ''}
 * @property string $dPhone {default ''}
 * @property string $dStreet {default ''}
 * @property string $dCity {default ''}
 * @property string $dZip {default ''}
 * @property string $dInfo {default ''}
 * @property string $infotext {default ''}
 * @property string $infosStatus {default ''} {enum self::INFOS_STATUS_*}
 * @property string $infosNumber {default ''}
 * @property DateTimeImmutable|null $infosCreatedTime
 * @property string $infosDocument {default ''}
 * @property string $infosResponse {default ''}
 * @property int|null $infosOrderStatusCode {enum self::INFOS_ORDER_STATUS_CODE_*}
 *
 * @property DateTimeImmutable|null $invoiceDate
 * @property int|null $invoiceNumber
 * @property DateTimeImmutable|null $mailConfirmationSend
 * @property DateTimeImmutable|null $mailPrepared
 * @property DateTimeImmutable|null $mailSent
 * @property DateTimeImmutable|null $mailStorno
 * @property DateTimeImmutable|null $mailProgress
 * @property string $barCode {default ''}
 * @property int $statsRead {default 0}
 * @property int|null $enableHeurekaQuery {default 0}
 * @property int $isDeleted {default 0}
 * @property string $hash
 * @property string|null $extId
 * @property DateTimeImmutable|null $exportTime
 *
 * PLATEBNI BRANA
 * @property int|null $paymentId
 * @property string|null $paymentJson
 * @property string|null $paymentStatus {enum self::ONLINE_PAYMENT_*}
 *
 *
 *
 * RELATIONS
 * @property OrderItem[]|OneHasMany $items {1:m OrderItem::$order}
 * @property User|null $user {m:1 User::$orders}
 * @property Mutation $mutation {m:1 Mutation::$orders} {default 1}
 * @property State|null $state {m:1 State::$orders} {default 1}
 * @property State|null $dState {m:1 State::$dOrders}
 * @property MutationTransports $mutationTransport {m:1 MutationTransports::$orders}
 * @property MutationPayments $mutationPayment {m:1 MutationPayments::$orders}
 *
 *
 * VIRTUAL
 * @property-read OrderItem[]|ICollection $basketItems {virtual}
 * @property-read OrderItem[]|ICollection $products {virtual}
 * @property-read OrderItem[]|ICollection $sets {virtual}
 * @property-read OrderItem[]|ICollection $parentItems {virtual}
 * @property-read OrderItem[]|ICollection $presents {virtual}
 * @property-read OrderItem[]|ICollection $services {virtual}
 * @property-read OrderItem[]|ICollection $rents {virtual}
 * @property-read OrderItem|null $transport {virtual}
 * @property-read OrderItem|null $payment {virtual}
 * @property-read OrderItem[]|ICollection $vouchers {virtual}
 * @property-read OrderItem[]|ICollection $discounts {virtual}
 * @property-read float $totalPriceDPH {virtual}
 * @property-read float $totalPrice {virtual}
 * @property-read float $revenueGTM {virtual}
 * @property-read float $p {virtual}
 * @property-read float $productsPriceDPH {virtual}
 * @property-read string $paymentName {virtual}
 * @property-read string $name {virtual}
 * @property-read string $dName {virtual}
 * @property bool $productMissing {virtual} {default false}
 * @property array $uniqueKeyErrors {virtual} {default []}
 * @property-read bool $hasDeliveryAddress {virtual}
 *
 * PLATEBNI BRANA
 * @property-read bool $isPayed {virtual}
 */
class Order extends Entity
{

	use hasFormDefaultDataTrait;
	use hasConfigServiceTrait;
	use hasCacheTrait;
	use hasConstsTrait;


	public const ONLINE_PAYMENT_CREATED = 'CREATED';  // založena
	public const ONLINE_PAYMENT_PAYMENT_METHOD_CHOSEN = 'PAYMENT_METHOD_CHOSEN'; //Platební metoda vybrána
	public const ONLINE_PAYMENT_PAID = 'PAID'; // Platba zaplacena
	public const ONLINE_PAYMENT_AUTHORIZED = 'AUTHORIZED'; // Platba předautorizována
	public const ONLINE_PAYMENT_CANCELED = 'CANCELED'; // Platba zrušena
	public const ONLINE_PAYMENT_TIMEOUTED = 'TIMEOUTED'; // Vypršelá platnost platby
	public const ONLINE_PAYMENT_REFUNDED = 'REFUNDED'; // refundována
	public const ONLINE_PAYMENT_PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'; // refundována
	public const ONLINE_PAYMENT_ERROR = 'error';

	public const STATUS_NEW = 'new'; // nova
	public const STATUS_WAITING = 'waiting'; // ceka na zaplaceni
	public const STATUS_PROGRESS = 'progress';// zpracovava se
	public const STATUS_SHIPPED = 'shipped'; // expedována
	public const STATUS_READY = 'ready'; // pripravena k odberu
	public const STATUS_PARTIALLY = 'partially'; // castecne vyrizena
	public const STATUS_DONE = 'done'; // vyrizena
	public const STATUS_CANCEL = 'cancel'; // stornovana

	public const TRANSPORT_PERSONAL = 'personal';
	public const TRANSPORT_PERSONAL_OSTRAVA = 'personalOstrava';
	public const TRANSPORT_PPL = 'ppl';
	public const TRANSPORT_PPLSK = 'pplSk';
	public const TRANSPORT_PPLEUROPE = 'pplEurope';
	public const TRANSPORT_POST = 'post';
	public const TRANSPORT_PERSONAL_SPECIAL = 'personalSpecial';
	public const TRANSPORT_ELECTRONIC = 'email';
	public const TRANSPORT_MESSENGER = 'messenger';

	public const PAYMENT_CASH = 'cash';
	public const PAYMENT_ONDELIVERY = 'onDelivery';
	public const PAYMENT_BANK = 'bank';
	public const PAYMENT_ONLINE = 'online';
	public const PAYMENT_INSTALLMENT = 'installment';


	public const INFOS_STATUS_EMPTY = '';
	public const INFOS_STATUS_OK = 'ok';
	public const INFOS_STATUS_ERROR = 'error';


	public const INFOS_ORDER_STATUS_CODE_0 = 0;
	public const INFOS_ORDER_STATUS_CODE_1 = 1;
	public const INFOS_ORDER_STATUS_CODE_2 = 2;
	public const INFOS_ORDER_STATUS_CODE_3 = 3;
	public const INFOS_ORDER_STATUS_CODE_4 = 4;
	public const INFOS_ORDER_STATUS_CODE_5 = 5;
	public const INFOS_ORDER_STATUS_CODE_6 = 6;
	public const INFOS_ORDER_STATUS_CODE_7 = 7;
	public const INFOS_ORDER_STATUS_CODE_8 = 8;
	public const INFOS_ORDER_STATUS_CODE_9 = 9;



	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterProducts(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => [OrderItem::TYPE_SET, OrderItem::TYPE_PRODUCT]]);
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterBasketItems(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => [OrderItem::TYPE_SET, OrderItem::TYPE_PRODUCT]]);
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterSets(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_SET]);
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterVouchers(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_VOUCHER, 'parentId' => null]);
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterDiscounts(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_DISCOUNT, 'parentId' => null]);
	}

	protected function getterTransport(): ?OrderItem
	{
		return $this->items->toCollection()->getBy(['type' => OrderItem::TYPE_TRANSPORT, 'parentId' => null]);
	}

	protected function getterPayment(): ?OrderItem
	{
		return $this->items->toCollection()->getBy(['type' => OrderItem::TYPE_PAYMENT, 'parentId' => null]);
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterPresents(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_PRESENT]);
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterServices(): ICollection
	{
		return $this->items->toCollection()->findBy(['type' => OrderItem::TYPE_SERVICE]);
	}


	protected function getterTotalPriceDPH(): float
	{
		$totalPriceDPH = 0;
		foreach ($this->items as $item) {
			$totalPriceDPH += $item->totalPriceDPH;
		}

		return $totalPriceDPH;
	}


	protected function getterTotalPrice(): float
	{
		$totalPrice = 0;
		foreach ($this->items as $item) {
			$totalPrice += $item->totalPrice;
		}

		return $totalPrice;
	}


	protected function getterProductsPriceDPH(): float
	{
		$priceDPH = 0;
		foreach ($this->products as $product) {
			$priceDPH += $product->totalPriceDPH;
		}

		return $priceDPH;
	}


	protected function getterIsPayed(): bool
	{
		return $this->paymentType === self::PAYMENT_ONLINE && $this->paymentStatus === self::ONLINE_PAYMENT_PAID;
	}

	/**
	 * @return OrderItem[]|ICollection
	 */
	protected function getterParentItems(): ICollection
	{
		return $this->items->toCollection()->findBy(['parentId' => null, 'type' => [OrderItem::TYPE_SET, OrderItem::TYPE_PRODUCT]]);
	}


	public function getRentsCount(): int
	{
		$count = 0;
		foreach ($this->rents as $i) {
			$count += $i->amount;
		}

		return $count;
	}


	protected function getterRevenueGTM(): float
	{
		return $this->totalPrice - ($this->payment?->totalPrice ?? 0.0) - ($this->transport?->totalPrice ?? 0.0);
	}


	protected function getterPaymentName(): string
	{
		$payments = $this->mutation->paymentsArray;

		if (isset($payments[$this->paymentType]) && isset($payments[$this->paymentType]['name'])) {
			return $payments[$this->paymentType]['name'];
		}

		return '';
	}

	protected function getterName(): string
	{
		return trim($this->firstname . ' ' . $this->lastname);
	}

	protected function getterDName(): string
	{
		return trim($this->dFirstname . ' ' . $this->dLastname);
	}

	protected function getterHasDeliveryAddress(): bool
	{
		return Strings::length($this->dCity) > 0;
	}

}
