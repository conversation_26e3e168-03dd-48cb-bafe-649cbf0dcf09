<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Repository\Repository;
use SuperKoderi\HasSimpleSave;

/**
 * @method ApiUser|null getById($id)
 * @method ApiUser|null getBy(array $conds)
 * @method ApiUser save(?ApiUser $entity, array $data)
 */
final class ApiUserRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [ApiUser::class];
	}

	public function create(string $username, string $password): ApiUser
	{
		return $this->save(null, ['username' => $username, 'password' => $password]);
	}

	public function getByUsername(string $username): ?ApiUser
	{
		return $this->getBy(['username' => $username]);
	}

	public function getByJwt(string $jwt): ?ApiUser
	{
		$parts = explode('.', $jwt);
		if (!isset($parts[1])) {
			return null;
		}

		if (($payload = base64_decode($parts[1], true)) === false) {
			return null;
		}

		$payload = json_decode($payload);

		if (!isset($payload->user)) {
			return null;
		}

		return $this->getByUsername($payload->user);
	}

}
