<?php declare(strict_types=1);

namespace App\Model;

use LogicException;
use App\Model\ElasticSearch\IndexModel;

class EsIndexFacade
{
	public function __construct(
		private IndexModel $indexModel,
		private EsIndexRepository $esIndexRepository,
		private ElasticSearch\All\Facade $allElasticFacade,
		private ElasticSearch\Product\Facade $productElasticFacade,
		private ElasticSearch\Common\Facade $commonElasticFacade,

	)
	{
	}


	public function fill(EsIndex $esIndex, int $limit = null, bool $autoSwitch = false): void
	{
		match ($esIndex->type) {
			EsIndex::TYPE_PRODUCT => $this->productElasticFacade->fill($esIndex, $limit, $autoSwitch),
			EsIndex::TYPE_ALL => $this->allElasticFacade->fill($esIndex, $limit, $autoSwitch),
			EsIndex::TYPE_COMMON =>  $this->commonElasticFacade->fill($esIndex, $limit, $autoSwitch),
			default => throw new LogicException('Missing definition for index'),
		};
	}



	public function delete(EsIndex $esIndex): void
	{
		$this->indexModel->getIndex($esIndex)->delete();
		$this->esIndexRepository->removeAndFlush($esIndex);
	}


}
