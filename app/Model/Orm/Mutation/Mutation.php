<?php

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use stdClass;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasConstsTrait;
use SuperKoderi\hasCustomFieldTrait;
use SuperKoderi\hasFormDefaultDataTrait;
use SuperKoderi\hasMutationTrait;
use SuperKoderi\IPagesFactory;
use SuperKoderi\Pages;

/**
 * @property int $id {primary}
 * @property int $public {default 1}
 * @property string $langCode {enum self::CODE_*}
 * @property string|null $name
 * @property string|null $langMenu
 *
 * @property string|null $adminEmail
 * @property string|null $contactEmail
 * @property string|null $orderEmail
 * @property string|null $fromEmail
 * @property string|null $fromEmailName
 * @property stdClass|Currency $currency {container JsonContainer}
 * @property ArrayHash $synonyms {container JsonContainer}
 * @property string|null $heurekaOverenoKey
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @x-property OneHasMany|Seolink[] $seolinks  {1:m Seolink::$mutation}
 * @property OneHasMany|EmailTemplate[] $emailTemplates  {1:m EmailTemplate::$mutation}
 * @property OneHasMany|EsIndex[] $esIndexes  {1:m EsIndex::$mutation}
 * @property ProductLocalization[]|OneHasMany $productLocalizations {1:m ProductLocalization::$mutation}
 * @property ProductVariantLocalization[]|OneHasMany $variantLocalizations {1:m ProductVariantLocalization::$mutation}
 * @property OneHasMany|Alias[] $aliases  {1:m Alias::$mutation}
 * @property OneHasMany|AliasHistory[] $historyAliases  {1:m AliasHistory::$mutation}
 *
 * @property OneHasMany|Order[]|null $orders  {1:m Order::$mutation}
 * @property OneHasMany|Voucher[] $vouchers  {1:m Voucher::$mutation}
 * @property OneHasMany|MutationTransports[]|null $transports {1:m MutationTransports::$mutation}
 * @property OneHasMany|MutationPayments[]|null $payments {1:m MutationPayments::$mutation}
 * @property ManyHasMany|User[]|null $users {m:m User::$mutations}
 *
 * @property OneHasMany|ProductVariantPrice[] $productVariantPrices  {1:m ProductVariantPrice::$mutation}
 * @property State[]|ManyHasMany $states {m:m State::$mutations, isMain=true}
 * @property Discount[]|ManyHasMany $discounts {m:m Discount::$mutations}
 * @property UserMutation[]|OneHasMany $userMutations {1:m UserMutation::$mutation}
 * @property NewsletterEmail[]|OneHasMany $newsletterEmails {1:m NewsletterEmail::$mutation}
 *
 *
 * VIRTUAL
 * @property-read Pages $pages {virtual}
 * @property-read Tree $rootPage {virtual}
 * @property-read string|null $domain {virtual}
 * @property-read string|null $urlPrefix {virtual}
 * @property-read string|null $region {virtual} https://app.sistrix.com/en/hreflang-generator
 * @property-read int $rootId {virtual}
 * @property-read int $eshopRootId {virtual}
 * @property-read int|null $hidePageId {virtual}
 * @property-read bool $isDefault {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property-read array $paymentsArray {virtual}
 *
 *
 */
class Mutation extends Entity
{
	use hasConfigServiceTrait;
	use hasCustomFieldTrait;
	use hasFormDefaultDataTrait;
	use hasConstsTrait;

	const CODE_CS = 'cs';
//	const CODE_SK = 'sk';
//	const CODE_EN = 'en';

	const SYNONYMS_DELIMITER = ',';

	const DEFAULT_CODE = self::CODE_CS; // urceni defaultni mutace
    const DEFAULT_RS_CODE = self::CODE_CS;

	private Pages $pagesObject;


	public function injectPages(IPagesFactory $pagesFactory): void
	{
		$this->pagesObject = $pagesFactory->create($this);

	}


	public function getRealDomain(): string
	{
		// todo remove
		return $this->domain;
	}


	public function getRealDomainWithoutWWW(): string
	{
		return str_replace('www.', '', $this->domain);
	}


	public function getRealRootId(): int
	{
		$config = $this->configService->get('mutations', $this->langCode, 'rootId');
		if ($config) {
			$realRootId = $config;
		} else {
			$realRootId = $this->rootId;
		}

		return (int)$realRootId;

	}

	public function getRealUrlPrefix(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'urlPrefix');
		if ($config) {
			$realUrlPrefix = $config;
		} else {
			$realUrlPrefix = $this->urlPrefix;
		}

		return str_replace('/', '', $realUrlPrefix);

	}

	public function getGTMCode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('mutations', $this->langCode);
		return isset($domain['gtmCode']) ? $domain['gtmCode'] : "";
	}

	public function getGACode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('mutations', $this->langCode);
		return isset($domain['googleAnalyticsCode']) ? $domain['googleAnalyticsCode'] : "";
	}


	public function getRealOrderEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'orderEmail');
		if ($config) {
			$realOrderEmail = $config;
		} else {
			$realOrderEmail = $this->orderEmail;
		}

		return $realOrderEmail;
	}


	public function getRealAdminEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'adminEmail');
		if ($config) {
			$realAdminEmail = $config;
		} else {
			$realAdminEmail = $this->adminEmail;
		}

		return $realAdminEmail;
	}


	/**
	 * @param string $key .. values = admin|contact|order|breeding
	 * @return bool|mixed
	 */
	public function getEmail(string $key = "admin")
	{
		$col = $key . 'Email';
		if (!isset($this->$col)) {
			return false;
		}
		$mutation = $this->configService->getParam('mutations', $this->langCode);

		if ($mutation && isset($mutation[$col]) && $mutation[$col]) {
			return $mutation[$col];
		} else {
			return $this->$col;
		}
	}


	public function getRealFromEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'fromEmail');
		if ($config) {
			$realEmailFrom = $config;
		} else {
			$realEmailFrom = $this->fromEmail;
		}

		return $realEmailFrom;
	}

	public function getRealFromEmailName(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'fromEmailName');
		if ($config) {
			$realFromEmailName = $config;
		} else {
			$realFromEmailName = $this->fromEmailName;
		}

		return $realFromEmailName;
	}


	public function getBaseUrl(): string
	{
		return 'https://' . $this->domain;
	}


	public function getBaseUrlWithPrefix(): string
	{
		if ((bool)$this->urlPrefix) {
			return 'https://' . $this->domain . '/' . $this->urlPrefix;
		} else {
			return 'https://' . $this->domain;
		}
	}


	public function getUrl(): string
	{
		return $this->getBaseUrlWithPrefix();
	}


	protected function getterPages(): Pages
	{
		return $this->pagesObject;
	}

	protected function getterRootPage(): ?Tree
	{
		$orm = $this->getRepository()->getModel();
		assert($orm instanceof Orm);
		return $orm->tree->getById($this->getRealRootId());
	}

	protected function getterHidePageId(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'hidePageId');
	}


	protected function getterRootId(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'rootId');
	}

	protected function getterEshopRootId(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'eshopRootId');
	}

	protected function getterRegion(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'region');
	}


	protected function getterUrlPrefix(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'urlPrefix');
	}


	protected function getterDomain(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'domain');
	}


	protected function getterIsDefault(): bool
	{
		return $this->langCode == self::DEFAULT_CODE;
	}


	public function getTransport(State $state): array
	{
		$transports = [];

		foreach ($this->transports->toCollection()->findBy([
			'state' => $state,
			'public' => 1,
		])->orderBy('freeFrom') as $item) {
			/** @var MutationTransports $item */
			$transports[$item->key] = $item->toArray();
			$transports[$item->key]['transport'] = $item;
		}
		return $transports;
	}

	protected function getterPaymentsArray(): array
	{
		$payments = [];
		foreach ($this->payments->toCollection()->findBy([
			'public' => 1
		])->orderBy('priceDPH') as $item) {
			/** @var MutationPayments $item */
			$payments[$item->key] = $item->toArray();
		}

		return $payments;
	}

}


class Currency
{
	public string $code;
	public string $symbol;
	public float $exchangeRate;
	public int $decimals;
	public string $decimalSeparator;
	public string $thousandSeparator;
	public string $symbolPosition;
}

class VatRate
{
	const DEFAULT = 'default';
	const LOW = 'low';
	const LOW_EXTRA = 'lowExtra';
	const LOW_ULTRA = 'lowUltra';
}
