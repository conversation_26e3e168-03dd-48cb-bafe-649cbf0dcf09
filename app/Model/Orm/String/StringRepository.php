<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use SuperKoderi\HasSimpleSave;

/**
 * @method StringEntity|null getById($id)
 * @method StringEntity|null getBy(array $conds)
 * @method StringEntity save(?StringEntity $entity, array $data)
 * @method null replace(array $data)
 * @method ICollection findNotParams()
 */
final class StringRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [StringEntity::class];
	}


	public function setTranslation(Mutation $mutation, string $name, string $value): StringEntity
	{
		$entity = $this->getByName($mutation, $name);
		return $this->save($entity, ['name' => $name, 'value' => $value, 'lg' => $mutation->langCode]);
	}

	public function getByName(Mutation $mutation, string $name): ?StringEntity
	{
		return $this->getBy(['lg' => $mutation->langCode, 'name' => $name]);
	}

	public function delete(string $name): void
	{
		foreach ($this->findBy(['name' => $name]) as $item) {
			$this->remove($item);
		}
	}

}
