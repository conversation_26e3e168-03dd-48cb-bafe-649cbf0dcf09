<?php

namespace App\Model;

use App\Exceptions\LogicException;
use App\PostType\Core\Model\ParentEntity;
use Nette\DeprecatedException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\IRelationshipCollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\ConfigService;
use SuperKoderi\hasCacheTrait;
use SuperKoderi\hasCustomFieldTrait;
use SuperKoderi\hasFormDefaultDataTrait;
use SuperKoderi\hasParameterRepositoryTrait;
use SuperKoderi\hasParametersTrait;
use SuperKoderi\MoneyHelper;
use SuperKoderi\MutationHolder;

/**
 * @property int $id {primary}
 * @property string|null $internalName
 * @property string $template {default 'Product:detail'}
 * @property DateTimeImmutable $publicFrom {default now}
 * @property DateTimeImmutable $publicTo {default '+100 year'}
 * @property string|null $uid
 * @property int|null $hideFirstImage {default 0}
 * @property int|null $soldCount {default 0}
 * @property int $storeLimit {default 0}
 * @property string|null $availableServices
 * @property int|null $discount
 * @property int|null $discountType
 * @property int|null $isSet
 * @property int|null $isMikrosvin
 * @property int|null $isAction
 * @property int $isVoucher {default 0}
 * @property int|null $isIconic
 * @property int|null $isSale
 * @property DateTimeImmutable|null $isNewExpirationTime {default '+60 day'}
 * @property int $isNew {default 0}
 * @property int|null $voucherCat {default null}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property float $reviewAverage {default 0}
 * @property string $availability {enum self::AVAILABILITY_*} {default self::AVAILABILITY_}
 * @property array $vats {container JsonContainer}
 * @property string|null $extId
 *
 * RELATIONS
 * @property ProductComment[]|OneHasMany $comments {1:m ProductComment::$product, orderBy=[date=ASC], cascade=[persist, remove]}
 * @property ProductReview[]|OneHasMany $reviews {1:m ProductReview::$product, orderBy=[date=ASC], cascade=[persist, remove]}
 * @property ProductVariant[]|OneHasMany $variants {1:m ProductVariant::$product, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property ProductImage[]|OneHasMany $images {1:m ProductImage::$product, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property ProductTree[]|OneHasMany $productTrees {1:m ProductTree::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property ProductLocalization[]|OneHasMany $productLocalizations {1:m ProductLocalization::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property ProductContent[]|OneHasMany $contents {1:m ProductContent::$product, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property ParameterValue[]|ManyHasMany $parametersValues {m:m ParameterValue::$products, isMain=true, orderBy=[parameterSort=ASC, sort=ASC]}
 * @property GroupProduct[]|OneHasMany $groupProductRel {1:m GroupProduct::$product, cascade=[persist, remove]}
 * @property ProductParameterIcon[]|OneHasMany $paramsIcons {1:m ProductParameterIcon::$product, cascade=[persist, remove]}
 *
 * @property TreeProduct[]|OneHasMany $treeProducts {1:m TreeProduct::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property ProductProduct[]|OneHasMany $productProducts {1:m ProductProduct::$mainProduct, cascade=[persist, remove]}
 * @property ProductProduct[]|OneHasMany $attachedProducts {1:m ProductProduct::$attachedProduct, cascade=[persist, remove]}
 * @property Discount[]|ManyHasMany $discounts {m:m Discount::$products, cascade=[persist]}
 *
 * @property UserFavoriteProduct[]|OneHasMany $userFavoriteProducts {1:m UserFavoriteProduct::$product, orderBy=[createdAt=ASC]}
 * @property ManyHasMany|ProductTagParent[] $productTagParents  {m:m ProductTagParent, isMain=true, oneSided=true}
 *
 *
 * VIRTUALS
 *
 *
 * @property int $public {virtual}
 *
 * @property-read CatalogTree[]|ICollection $inCategories {virtual}
 * @property-read CatalogTree[]|ICollection $inOwnCategories {virtual}
 *
 * @property-read string|null $name {virtual}
 * @property-read string|null $annotation {virtual}
 * @property-read string|null $nameTitle {virtual}
 * @property-read string|null $nameAnchor {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string|null $keywords {virtual}
 * @property-read string|null $content {virtual}
 * @property-read string $nameVariant {virtual}
 * @property-read string $detailTitle {virtual}
 *
 * @property ProductVariant|null $firstVariant {virtual}
 * @property ArrayHash|null $cf {virtual}
 *
 * @property-read Tree[]|ICollection $pages {virtual}
 * @property-read Tree[]|ICollection $pagesAll {virtual}
 *
 * @property-read CatalogTree[]|ICollection $attachCategories {virtual}
 * @property-read CatalogTree[]|ICollection $attachCategoriesAll {virtual}
 *
 * @property-read Product[]|ICollection $accessories {virtual}
 * @property-read Product[]|ICollection $accessoriesAll {virtual}
 *
 * @property-read Product[]|ICollection $similarProducts {virtual}
 * @property-read Product[]|ICollection $similarProductsAll {virtual}
 * @property-read Product[]|ICollection $presents {virtual}
 * @property-read Product[]|ICollection $presentsAll {virtual}
 *
 * @property-read Product[]|ICollection $products {virtual}
 * @property-read Product[]|ICollection $productsAll {virtual}
 * @property-read array $productsCounts {virtual}
 *
 * @property ProductImage|null $firstImage {virtual}
 * @property array $reviewInfo {virtual}
 * @property array|null $videos {virtual}
 * @property ProductFile[]|OneHasMany $files {virtual}
 * @property array|null $links {virtual}
 * @property-read Parameter[]|ICollection $variantParameters {virtual}
 * @property-read bool $hasShellVariant {virtual}
 * @property-read array|null $path {virtual}
 * @property-read Supply[]|ICollection $supplies {virtual}
 * @property-read ProductVariant[]|ICollection $activeVariants {virtual}
 * @property-read ProductVariant[]|ICollection $activeVariantsGroup {virtual}
 * @property-read bool $isInStock {virtual} true = skladem alespon jedna varianta
 * @property-read int $totalSupplyCount {virtual}
 * @property-read ArrayHash $supplyInfo {virtual}
 * @property-read bool|null $isVariant {virtual}
 * @property-read string $cacheId {virtual}
 * @property-read string $groupString {virtual}
 * @property-read CatalogTree|null $mainCategory {virtual}
 *
 * -------WINE PARAMETERS------
 * @property-read string|null $wineColor {virtual}
 * @property-read string|null $wineSeries {virtual}
 * @property-read string|null $wineViticulture {virtual}
 * @property-read string|null $wineYear {virtual}
 * @property-read string|null $wineSugar {virtual}
 * @property-read string|null $wineVariety {virtual}
 * @property-read string|null $wineQualityClass {virtual}
 * @property-read string|null $wineStorage {virtual}
 * @property-read string|null $wineAcidity {virtual}
 * @property-read string|null $wineAlcohol {virtual}
 * @property-read string|null $wineBatchNumber {virtual}
 *
 * @property-read array|null $residualSugar {virtual}
 * @property-read ProductTag[] $productTags {virtual}
 *
 */
class Product extends BaseEntity implements ParentEntity
{
	use hasParametersTrait;
	use hasFormDefaultDataTrait;
	use hasParameterRepositoryTrait;
	use hasCacheTrait;
	use hasCustomFieldTrait;

	public const AVAILABILITY_ON_STOCK = 'onStock';
	public const AVAILABILITY_ASK = 'ask';
	public const AVAILABILITY_WEEK = 'week';
	public const AVAILABILITY_ = '';

	private ConfigService $configService;

	private TreeRepository $treeRepository;

	private ParameterRepository $parameterRepository;

	private SupplyRepository $supplyRepository;

	private ProductVariantRepository $productVariantRepository;

	private ProductRepository $productRepository;

	private MutationHolder $mutationHolder;

	private StateModel $stateModel;

	private ?Mutation $mutation = null;

	private CatalogTreeModel $catalogTreeModel;
	private ?PriceLevel $discountPriceLevel = null;

	public function injectService(
		TreeRepository $treeRepository,
		ConfigService $configService,
		ParameterRepository $parameterRepository,
		SupplyRepository $supplyRepository,
		ProductRepository $productRepository,
		ProductVariantRepository $productVariantRepository,
		MutationHolder $mutationHolder,
		CatalogTreeModel $catalogTreeModel,
		StateModel $stateModel,
	): void
	{
		$this->productVariantRepository = $productVariantRepository;
		$this->parameterRepository = $parameterRepository;
		$this->treeRepository = $treeRepository;
		$this->productRepository = $productRepository;
		$this->supplyRepository = $supplyRepository;
		$this->configService = $configService;
		$this->mutationHolder = $mutationHolder;
		$this->catalogTreeModel = $catalogTreeModel;
		$this->stateModel = $stateModel;
	}


	protected function getterFirstImage(): ?ProductImage
	{
		return $this->images->toCollection()->fetch();
	}


	protected function getterReviewInfo(): array
	{
		if (!isset($this->cache['reviewInfo'])) {
			$cnt = 0;
			$sum = 0;
			foreach ($this->reviews as $i) {
				$cnt++;
				$sum += $i->stars;
			}

			$this->cache['reviewInfo'] = [
				'count' => $cnt,
				'percent' => $cnt > 0 ? round(($sum / $cnt) * 20) : 0,
//			'rank' => $sum/$cnt,
			];
		}

		return $this->cache['reviewInfo'];
	}


	protected function getterCf(): mixed
	{
		return $this->getLocalization(
			$this->getMutation()
		)->cf;
	}


	protected function getterPublic(): int
	{
		$productLocalization = $this->getLocalization(
			$this->getMutation()
		);
		if ($productLocalization !== null) {
			return $productLocalization->public;
		}

		return 0;
	}


	protected function getterFiles(): IRelationshipCollection
	{
		return $this->getLocalization(
			$this->getMutation()
		)->files;
	}




	// ************************************ Price ************************************************

	private function fillPriceCache(Mutation $mutation, PriceLevel $priceLevel, State $state): void
	{
		if (!isset($this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'])) {
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'] = 0.0;
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'] = 0.0;
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['isPriceFrom'] = false;
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'] = null;
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['isDiscountFrom'] = false;


			foreach ($this->activeVariants as $variant) {
				$priceWithVat = $variant->priceWithVat($mutation, $priceLevel, $state);
				$originalPriceWithVat = $variant->originalPriceWithVat($mutation, $priceLevel, $state);
				$discountPriceWithVat = $variant->discountPriceWithVat($mutation, $state);

				if (!$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat']) {
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'] = $priceWithVat;
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'] = $originalPriceWithVat;
				}

				if ($discountPriceWithVat !== null) {
					if (isset($this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'])) {
						if ($this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat']->price < $discountPriceWithVat->price) {
							$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'] = $discountPriceWithVat;
						}
					} else {
						$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'] = $discountPriceWithVat;
					}
				}

				if ($priceWithVat !== $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat']) {
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['isPriceFrom'] = true; // = ceny PV jsou ruzne
				}

				if ($priceWithVat < $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat']) {
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'] = $priceWithVat;
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'] = $originalPriceWithVat;
				}
			}
		}
	}


	public function priceWithVat(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'];
	}


	public function originalPriceWithVat(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'];
	}


	public function discountPriceWithVat(Mutation $mutation, State $state): ?ProductVariantPrice
	{
		$priceLevel = $this->getDiscountPriceLevel();

		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'];
	}


	public function priceVat(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$value = MoneyHelper::getPriceWithoutVat($this->priceWithVat($mutation, $priceLevel, $state), $this->vat($state));
		return MoneyHelper::round($value, $mutation->currency->decimals);
	}

	public function originalPrice(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$value = MoneyHelper::getPriceWithoutVat($this->originalPriceWithVat($mutation, $priceLevel, $state), $this->vat($state));
		return MoneyHelper::round($value, $mutation->currency->decimals);
	}


	public function hasPriceFrom(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['isPriceFrom'];
	}

	private function getDiscountPriceLevel(): PriceLevel
	{
		if ($this->discountPriceLevel === null) {

			$orm = $this->getRepository()->getModel();
			assert($orm instanceof Orm);
			$priceLevelRepository = $orm->priceLevel;
			assert($priceLevelRepository instanceof PriceLevelRepository);

			$this->discountPriceLevel = $priceLevelRepository->getByChecked(['type'=>PriceLevel::TYPE_PRICE_ACTION]);
		}

		return $this->discountPriceLevel;
	}

	public function getterPages(): ICollection
	{
		return $this->pagesAll->findBy($this->treeRepository->getPublicOnlyWhereParams())->findBy(['rootId' => $this->getMutation()->rootId]);
	}


	public function getterPagesAll(): ICollection
	{
		return $this->treeRepository->findTreesInTreeProductRelations($this, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $this->getMutation());
	}


	protected function getterPresents(): ICollection
	{
		return $this->presentsAll->findBy($this->productRepository->getPublicOnlyWhere());
	}


	protected function getterPresentsAll(): ICollection
	{
		return $this->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_PRESENT);

	}


	protected function getterAccessories(): ICollection
	{
		return $this->accessoriesAll->findBy($this->productRepository->getPublicOnlyWhere());
	}


	protected function getterAccessoriesAll(): ICollection
	{
		return $this->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_ACCESSORY);
	}


	protected function getterSimilarProducts(): ICollection
	{
		return $this->similarProductsAll->findBy($this->productRepository->getPublicOnlyWhere());
	}


	protected function getterSimilarProductsAll(): ICollection
	{
		return $this->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_SIMILAR);

	}


	public function getterProductsCounts(): array
	{
		$orm = $this->getRepository()->getModel();
		assert($orm instanceof Orm);

		if ($this->isSet) {
			$type = ProductProduct::TYPE_SET;
		} else {
			$type = ProductProduct::TYPE_NORMAL;
		}

		$productProducts = $orm->productProduct->findByMain($this, $type);
		$ret = [];
		foreach ($productProducts as $productProduct) {
			assert($productProduct instanceof ProductProduct);
			if (isset($ret[$productProduct->attachedProduct->id])) {
				$ret[$productProduct->attachedProduct->id]++;
			} else {
				$ret[$productProduct->attachedProduct->id] = 1;
			}
		}
		return $ret;
	}


	public function getterProducts(): ICollection
	{
		return $this->productsAll->findBy($this->productRepository->getPublicOnlyWhere());
	}


	public function getterProductsAll(): ICollection
	{
		if ($this->isSet) {
			$type = ProductProduct::TYPE_SET;
		} else {
			$type = ProductProduct::TYPE_NORMAL;
		}

		return $this->productRepository->findAttachedProductsInRelations($this, $type);
	}


	protected function getParamsSorts(): array
	{
		if (!isset($this->cache['getParamsSorts'])) {
			$ret = [];
			foreach ($this->parameterRepository->findAll()->orderBy(['sort' => 'DESC']) as $i) {
				if ($i->parent) {
					// + zohledneni poradi rodice
					$ret[$i->id] = $i->parent->sort . "" . sprintf("%'.03d\n", $i->sort);
				}
			}
			$this->cache['getParamsSorts'] = $ret;
		}
		return $this->cache['getParamsSorts'];
	}


	protected function getterAttachCategories(): ICollection
	{
		return $this->attachCategoriesAll->findBy($this->treeRepository->getPublicOnlyWhere());
	}


	protected function getterAttachCategoriesAll(): ICollection
	{
		return $this->treeRepository->findBy([
			'productTrees->product->id' => $this->id,
			'type' => Tree::TYPE_CATALOG
		])->orderBy('productTrees->sort');
	}


	protected function getterLinks(): mixed
	{
		return $this->getLocalization(
			$this->getMutation()
		)->links;
	}


	protected function getterMainCategory(): ?CatalogTree
	{
		$localization = $this->getLocalization(
			$this->getMutation()
		);
		return $this->catalogTreeModel->getMainCategoryForProductLocalization($localization);
	}

	// ************************************ Stock ************************************************

	public function getterSupplyInfo(): ArrayHash
	{
		if (!isset($this->cache['supplyInfo'])) {
			$supplyInfo = new ArrayHash();
			$supplyInfo->total = 0;
			$supplyInfo->someNotAvailable = false;

			foreach ($this->activeVariants as $variant) {
				$supplyInfo->total += $variant->totalSupplyCount;

				if (!$variant->isInStock) {
					$supplyInfo->someNotAvailable = true;
				}
			}
			$this->cache['supplyInfo'] = $supplyInfo;
		}

		return $this->cache['supplyInfo'];
	}


	protected function getterTotalSupplyCount(): int
	{
		return $this->supplyInfo->total;
	}


	protected function getterIsInStock(): bool
	{
		return $this->totalSupplyCount > 0;
	}


	protected function getterMaxSupplies(): ICollection
	{
		return $this->supplyRepository->findBy([
			'variant' => $this->activeVariants->fetchPairs(null, 'id'),
		]);
	}

	protected function getterSupplies(): ICollection
	{
		return $this->supplyRepository->findBy([
			'variant' => $this->activeVariants->fetchPairs(null, 'id'),
		]);
	}


	protected function getterFirstVariant(): ?ProductVariant
	{
		if ($this->variants->count()) {
			return $this->variants->toCollection()->fetch();
		} else {
			return null;
		}
	}


//	protected function getterSet()
//	{
//		if (!isset($this->id)) {
//			return new \ArrayIterator();
//		}
//		return $this->productRepository->findSetBySetpartId($this->id);
//	}


	protected function getterVideos(): mixed
	{
		return $this->getLocalization(
			$this->getMutation()
		)->videos;
	}


	public function getRootLang(): string
	{
		return 'cs';
	}


	/**
	 * @param State|null $state
	 * @return float
	 * @throws LogicException
	 */
	public function vat(State $state = null): float
	{
		$vatRates = $this->stateModel->getAllVatRatesValues();
		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRate = $this->vats->$stateId ?? VatRate::DEFAULT;

		if (!isset($vatRates[$stateId][$vatRate])) {
			throw new LogicException(sprintf('Unknown vatRate %s for Product ID %d, state ID %d', $vatRate, $this->id, $stateId));// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return (float) $vatRates[$stateId][$vatRate];
	}


	protected function getterVat(): never
	{
		throw new DeprecatedException('Abandoned getter - use function $object->vat() instead');

	}

	protected function getterVariantParameters(): ICollection
	{
		return $this->parameterRepository->findBy([
			'variantParameter' => 1
		]);
	}

	protected function getterHasShellVariant(): bool
	{
		return $this->variants->count() == 1 && $this->variants->toCollection()->fetch()->stringId == ProductVariant::SHELL_STRING_ID;
	}


	protected function getterPath(): array
	{
		$path = [];
		if (!$this->isPersisted()) {
			return $path;
		}

		$mainCategory = $this->mainCategory;
		if ($mainCategory !== null) {
			foreach ($mainCategory->pathItems as $item) {
				$path[] = $item->id;
			}
			$path[] = $mainCategory->id;
		}

		return $path;
	}


	protected function getterActiveVariants(): ICollection
	{
		return $this->variants->toCollection()->findBy([
			'variantLocalizations->active' => 1,
			'variantLocalizations->mutation' => $this->getMutation(),
		]);
	}


	protected function getterActiveVariantsGroup(): ICollection
	{
		$groupIds = [];
		$groupProducts = $this->groupProductRel->toCollection()->orderBy('sort');
		foreach ($groupProducts as $groupProduct) {
			$groupIds[] = $groupProduct->group->id;
		}

		if ($groupIds) {
			$productsIds = $this->productRepository->findBy(['groupProductRel->group' => $groupIds])->fetchPairs(null, 'id');
			$variants = $this->productVariantRepository->findBy(['variantLocalizations->ctive' => 1, 'product' => $productsIds])->orderBy('product->groupProductRel->sort')->orderBy('sort');
//			dump($variants->fetchAll());
		} else {
			$variants = $this->activeVariants;
		}

		return $variants;
	}


	protected function getterGroupString(): string
	{
		if ($this->groupProductRel->count()) {
			$firstGroup = $this->groupProductRel->toCollection()->fetch();
			return 'g-' . $firstGroup->id;
		} else {
			return 'p-' . $this->id;
		}
	}


	protected function getterIsVariant(): bool
	{
		return FALSE;
	}


	protected function getterCacheId(): string
	{
		return 'prod' . $this->id;
	}


	public function setMutation(Mutation $mutation): void
	{
		if ( $this->getMutation() !== null && $mutation->id === $this->getMutation()->id) {
			return;
		}

		$this->flushCache();

		foreach ($this->variants as $variant) {
			$variant->flushCache();
		}

		$this->mutation = $mutation;
	}


	public function getMutation(): Mutation
	{
		if ($this->mutation !== null) {
			return $this->mutation;
		} else {
			return $this->mutationHolder->getMutation();
		}
	}


	public function getLocalization(Mutation $mutation): ?ProductLocalization
	{
		return $this->productLocalizations->toCollection()->getBy(['mutation' => $mutation]);
	}


	protected function getterName(): ?string
	{
		return $this->getLocalization($this->getMutation())?->name;
	}


	protected function getterNameAnchor(): ?string
	{
		return $this->getLocalization($this->getMutation())?->nameAnchor;
	}


	protected function getterNameTitle(): ?string
	{
		return $this->getLocalization($this->getMutation())?->nameTitle;
	}


	protected function getterDescription(): ?string
	{
		return $this->getLocalization($this->getMutation())?->description;
	}


	protected function getterKeywords(): ?string
	{
		return $this->getLocalization($this->getMutation())?->keywords;
	}


	protected function getterContent(): ?string
	{
		return $this->getLocalization($this->getMutation())?->content;
	}


	protected function getterAnnotation(): ?string
	{
		return $this->getLocalization($this->getMutation())?->annotation;
	}

	/**
	 * Kvuli sjednoceni API P a PV (tam, kde v $object muze byt instance obojiho)
	 * @return string
	 */
	public function getterNameVariant(): string
	{
		return '';
	}

	/**
	 * @return array|CatalogTree[]
	 */
	protected function getterInCategories(): array
	{
		if (!isset($this->cache['inCategories'])) {
			$allCats = $this->catalogTreeModel->getAllCatalogCategories($this, $this->getMutation());
			$inCats = [];
			foreach ($allCats as $cat) {
				if ($cat->level > 1) {
					$inCats[] = $cat;
				}
			}
			$this->cache['inCategories'] = $inCats;
		}
		return $this->cache['inCategories'];
	}

	/**
	 * @return array|CatalogTree[]
	 */
	protected function getterInOwnCategories(): array
	{
		if (!isset($this->cache['inOwnCategories'])) {
			$allCats = $this->catalogTreeModel->getAllCatalogCategories($this, $this->getMutation());
			$inCats = [];
			foreach ($allCats as $cat) {
				if ($cat->level > 1) {
					$inCats[] = $cat;
				}
			}
			$this->cache['inOwnCategories'] = $inCats;
		}
		return $this->cache['inOwnCategories'];
	}

	public function getterWineColor(): ?string
	{
		return $this->getParameterValueByUid("color")?->value;
	}

	public function getterWineSeries(): ?string
	{
		return $this->getParameterValueByUid("series")?->value;
	}

	public function getterWineYear(): ?string
	{
		return $this->getParameterValueByUid("year")?->value;
	}

	public function getterWineSugar(): ?string
	{
		return $this->getParameterValueByUid("sugar")?->value;
	}

	public function getterWineVariety(): ?string
	{
		return $this->getParameterValueByUid("variety")?->value;
	}

	public function getterWineQualityClass(): ?string
	{
		return $this->getParameterValueByUid("qualityClasses")?->value;
	}

	public function getterWineViticulture(): ?string
	{
		return $this->getParameterValueByUid("viticulture")?->value;
	}

	public function getterWineStorage(): ?string
	{
		return $this->getParameterValueByUid("storage")?->value;
	}

	public function getterWineAcidity(): ?string
	{
		return $this->getParameterValueByUid("acidity")?->value;
	}

	public function getterWineAlcohol(): ?string
	{
		return $this->getParameterValueByUid("alcohol")?->value;
	}

	public function getterSugar(): ?array
	{
		$residualSugarArray = null;
		$sugar = $this->wineSugar;
		$bySugar = $this->getParameterValueByUid("bySugar")?->value;
		if ($sugar !== null && $bySugar !== null) {
			$sugarMinMax = $this->getSugarMinMax($bySugar);
			$residualSugarArray["min"] = $sugarMinMax["min"];
			$residualSugarArray["max"] = $sugarMinMax["max"];
			$residualSugarArray["value"] = floatval($sugar);
			return $residualSugarArray;
		} else {
			return null;
		}
	}

	public function getterWineBatchNumber(): ?string
	{
		return $this->getParameterValueByUid("batchNumber")?->value;
	}

	public function getterYear(): ?string
	{
		return $this->getParameterValueByUid("year")?->value;
	}

	public function getterDetailTitle(): ?string
	{
		$params = [
			$this->wineViticulture,
			$this->wineSeries
		];
		if (empty($params = array_filter($params, function ($a) { return $a !== null;})))  {
			return "";
		}
		$detailTitle = $this->wineQualityClass !== null ? implode(" - ", $params) . ", " . $this->wineQualityClass : implode(" - ", $params);

		$bySugarValue = $this->getParameterValueByUid("bySugar")?->value;

		return ($bySugarValue !== null) ? $detailTitle . ', ' . $bySugarValue : $detailTitle;
	}

	private function getSugarMinMax(string $bySugar): array
	{
		switch ($bySugar) {
			case "brut": //brut
				return [
					"min" => 3,
					"max" => 15
				];
			case "suché": //suché
				return [
					"min" => 0,
					"max" => 9
				];
			case "polosuché": //polosuché
				return [
					"min" => 4,
					"max" => 18
				];
			case "polosladké": //polosladké
				return [
					"min" => 12,
					"max" => 45
				];
			case "sladké": //sladké
				return [
					"min" => 45.1,
					"max" => 250
				];
			default:
				throw new \Exception("Unknown sugar type");
		}
	}


	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->productLocalizations->toCollection();
	}

	public function getSuggestName(): string
	{
		$parts = [$this->internalName];

		if ($year = $this->getParameterValueByUid("year")?->value) {
			$parts[] = 'ročník: ' . $year;
		}
		if ($batchNumber = $this->getParameterValueByUid("batchNumber")?->value) {
			$parts[] = 'šarže: ' . $batchNumber;
		}
		if ($intCislo = $this->getParameterValueByUid("intCislo")?->value) {
			$parts[] = 'interní číslo: ' . $intCislo;
		}


		return implode(' - ', $parts);
	}

	protected function getterProductTags(): ICollection
	{
		$productTagParentIds = $this->productTagParents->toCollection()->fetchPairs(null, 'id');
		if ($productTagParentIds !== []) {
			/** @var Orm $orm */
			$orm = $this->getRepository()->getModel();

			return $orm->productTag->findBy([
				'mutation' => $this->getMutation(),
				'parent' => $productTagParentIds,
			]);
		}

		return new EmptyCollection();
	}
}
