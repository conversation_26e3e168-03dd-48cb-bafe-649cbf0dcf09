<?php declare(strict_types = 1);

namespace App\Model;

use \SuperKoderi\CustomField\CustomFields;
use App\Model\ParameterValue;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Orm\Entity\IEntity;

class ParameterValueModel
{

	public function __construct(
		private Orm $orm,
		private readonly CustomFields $customFields,
	) {}


	/**
	 * @param iterable<ParameterValue> $parameterValues
	 */
	public function handleParameterValuesAlias(iterable $parameterValues): void
	{
		$aliases = [];
		foreach ($parameterValues as $parameterValue) {
			$aliases[$parameterValue->id] = $parameterValue->internalAlias;
		}

		foreach ($parameterValues as $parameterValue) {
			$aliases[$parameterValue->id] = $this->handleParameterValueAlias($parameterValue, $aliases);
		}
	}


	private function handleParameterValueAlias(ParameterValue $parameterValue, array $aliases): string
	{
		unset($aliases[$parameterValue->id]);

		if (in_array($parameterValue->internalAlias, $aliases)) {
			//change alias
			if (preg_match('/^(.*)-([1-9]+)$/', $parameterValue->internalAlias, $matches)) {
				$newId = $matches[2] + 1;
				$parameterValue->internalAlias = preg_replace('/-[1-9]+$/', '-' . $newId, $parameterValue->internalAlias);
			} else {
				$parameterValue->internalAlias .= '-1';
			}

			$this->handleParameterValueAlias($parameterValue, $aliases);
		}

		return $parameterValue->internalAlias;
	}


	public function removeValue(ParameterValue $parameterValue, IEntity $holder, bool $all = false): void
	{
		if ($holder instanceof Tree) {
			if ($all) {
				$this->orm->tree->removeParameter($holder, $parameterValue->parameter);
			} else {
				$this->orm->tree->removeParameterValue($holder, $parameterValue);
			}
		} elseif ($holder instanceof Product) {
			if ($all) {
				$this->orm->product->removeParameter($holder, $parameterValue->parameter);
			} else {
				$this->orm->product->removeParameterValue($holder, $parameterValue);
			}
		}

		if ($parameterValue->parameter->isSimple) {

			// is simple --> remove value
			$this->orm->parameterValue->remove($parameterValue);
		}
	}


	public function addValue(ParameterValue $parameterValue, IEntity $holder): void
	{
		if ($holder instanceof Tree) {
			$this->orm->tree->addParameterValue($holder, $parameterValue);
		} elseif ($holder instanceof Product) {
			$this->orm->product->addParameterValue($holder, $parameterValue);
		}
	}


	public function handleParameterValuesAttachment(IEntity $holder, Parameter $parameter, ?int $parameterValueId = null, mixed $newValue = null): void
	{
		if ($newValue || ($parameter->type === Parameter::TYPE_BOOL && $newValue === '0')) {
			if (!$parameterValueId) {
				$parameterValue = new ParameterValue();
				$parameterValue->internalValue = $newValue;
				$parameterValue->internalAlias = Strings::webalize($parameterValue->internalValue);
				$parameterValue->parameter = $parameter;
				$parameterValue->parameterSort = $parameter->sort;
				$this->orm->parameterValue->persistAndFlush($parameterValue);

				$this->addValue($parameterValue, $holder);
			} else {

				$parameterValue = $this->orm->parameterValue->getById($parameterValueId);
				if ($parameterValue->internalValue !== $newValue) {
					// update simple value
					$parameterValue->internalValue = $newValue;
					$parameterValue->internalAlias = Strings::webalize($parameterValue->internalValue);
					$this->orm->parameterValue->persistAndFlush($parameterValue);
					$this->addValue($parameterValue, $holder);
				}
			}
		} else {
			// delete simple value
			$parameterValue = $this->orm->parameterValue->getById($parameterValueId);
			if ($parameterValue) {
				$this->removeValue($parameterValue, $holder);
			}
		}
	}

	public function getVariantParameterValues(): array
	{
		$ret = [];

		foreach ($this->orm->parameter->findBy(['variantParameter' => 1]) as $parameter) {
			foreach ($parameter->options as $option) {
				$ret[$parameter->uid][$option->id] = $option;
			}
		}

		return $ret;
	}

	public function handleParameterValuesSelectAttachment(IEntity $holder, Parameter $parameter, mixed $parameterValuesData): void
	{
		assert(method_exists($holder, 'flushParam'));
		assert(method_exists($holder, 'getParameterValueById'));
		$holder->flushParam();
		$parameterValue = $this->orm->parameterValue->getById($parameterValuesData);
		$presentValue = $holder->getParameterValueById($parameter->id);

		if ($presentValue !== $parameterValue) {
			// budu pro select pridavat novou hodnotu
			// muzu smazat vsechny nynejsi
			if ($presentValue) {
				$this->removeValue($presentValue, $holder, true);
			}

			$this->addValue($parameterValue, $holder);
		}
	}

	public function handleParameterValuesMultiSelectAttachment(IEntity $holder, Parameter $parameter, mixed $parameterValuesData): void
	{
		assert(method_exists($holder, 'flushParam'));
		assert(method_exists($holder, 'getParameterValueById'));

		$holder->flushParam();
		$parameterValues = $this->orm->parameterValue->findByIds($parameterValuesData);
		$presentValues = $holder->getParameterValueById($parameter->id);

		$presentValuesTmp = [];

		if ($presentValues) {
			foreach ($presentValues as $presentValue) {
				$presentValuesTmp[$presentValue->id] = $presentValue;
			}
		}

		foreach ($parameterValues as $parameterValue) {
			\assert($parameterValue instanceof ParameterValue);
			if (isset($presentValuesTmp[$parameterValue->id])) {
				// zasilana hodnota je v present hodnotach -- neni potreba nic delat
				unset($presentValuesTmp[$parameterValue->id]);
			} else {
				$this->addValue($parameterValue, $holder);
			}

			$this->addValue($parameterValue, $holder);
		}

		foreach ($presentValuesTmp as $presentValue) {
			// odstranit stare hodnoty ktere nejsou nove zasílány
			$this->removeValue($presentValue, $holder);
		}
	}

	public function saveValueDetail(ParameterValue $parameterValue, array $valuesAll): void
	{
		if (isset($valuesAll['customFields'])) {
			$parameterValue->cf = $this->customFields->prepareDataToSave($valuesAll['customFields']);
		} else {
			$parameterValue->cf = new ArrayHash();
		}

		$this->orm->parameterValue->persistAndFlush($parameterValue);
	}

}
