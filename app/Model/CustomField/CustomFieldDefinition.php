<?php

declare(strict_types=1);

namespace SuperKoderi\CustomField;

use App\Model\AliasRepository;
use App\Model\LibraryImageRepository;
use App\Model\Orm;
use App\Model\ParameterRepository;
use App\Model\ParameterValue;
use App\Model\ParameterValueRepository;
use App\Model\Product;
use App\Model\ProductRepository;
use App\Model\ProductTagParent;
use App\Model\ProductTagParentRepository;
use App\Model\SeoLinkLocalizationRepository;
use App\Model\TreeRepository;

final class CustomFieldDefinition extends \stdClass
{
	/**
	 * @param array<string, mixed> $definition
	 */
	public function __construct(array $definition)
	{
		foreach ($definition as $key => $value) {
			$this->$key = $value;
		}
	}

	public static function extend(self $definition, array $localDefinition): self
	{
		$self = clone $definition;
		foreach ($localDefinition as $key => $value) {
			$self->$key = $value;
		}

		return $self;
	}

	public function process(
		mixed $value,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		$type = $this->type;
		return match ($type) {
			'list' => $this->processList($value, $inRs, $orm, $lazyValueFactory),
			'group' => $this->processGroup($value, $inRs, $orm, $lazyValueFactory),
			'suggest' => $this->processSuggest($value, $inRs, $orm, $lazyValueFactory),
			'image' => $this->processImage($value, $inRs, $orm, $lazyValueFactory),
			'file' => $this->processFile($value, $inRs, $orm, $lazyValueFactory),
			'select' => $this->processSelect($value, $inRs),
			default => $value,
		};
	}

	private function processList(
		mixed $value,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		if ( ! $inRs
			&& isset($this->items)
			&& \count($this->items) === 1
			&& \current($this->items)->type === 'suggest'
		) {
			$itemDefinition = \current($this->items);
			$repository = $this->getRepositoryBySubType($itemDefinition->subType ?? '', $orm);

			$tmpIds = [];
			foreach ($value as $row) {
				$tmpIds[] = current(is_array($row) ? $row : get_mangled_object_vars($row));
			}

			if ($repository instanceof TreeRepository) {
				return $repository->findFilteredPages($tmpIds)->findBy($repository->getPublicOnlyWhere());
			}

			if ($repository instanceof ProductRepository) {
				return $repository->findFilteredProducts($tmpIds)->findBy($repository->getPublicOnlyWhere());
			}

			return $repository->findByIds($tmpIds);
		}

		$processedItems = [];
		foreach ($value as $item) {
			$processedItem = $this->processListItem($item, $inRs, $orm, $lazyValueFactory);
			if ($processedItem == new \stdClass()) {
				continue;
			}

			$processedItems[] = $processedItem;
		}

		return $processedItems;
	}

	private function processGroup(
		mixed $value,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		foreach ($value as $group) {
			$processedItem = $this->processListItem($group, $inRs, $orm, $lazyValueFactory);
			if ($processedItem == new \stdClass()) {
				continue;
			}

			if ($inRs) {
				// only one item from items in RS mode
				return [$processedItem];
			} else {
				// return object instead of list with one value
				return $processedItem;
			}
		}

		return [];
	}

	private function processListItem(
		mixed $item,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		$processed = new \stdClass();

		foreach ($item as $key => $value) {
			if ( ! isset($this->items[$key])) {
				continue;
			}

			$definition = $this->items[$key];
			$processed->$key = $definition->process($value, $inRs, $orm, $lazyValueFactory);
		}

		return $processed;
	}

	private function processSuggest(
		mixed $value,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		$repository = $this->getRepositoryBySubType($this->subType ?? '', $orm);

		$id = (int) $value;
		if ( ! $id) {
			return null;
		}

		if ( ! $inRs) {
			return $lazyValueFactory->create($repository, $id);
		}

		$entity = $repository->getById($id);
		if ($entity === null) {
			return null;
		}

		if ($entity instanceof ProductTagParent) {
			return ['id' => $id, 'value' => $entity->internalName];
		}

		if ($entity instanceof ParameterValue) {
			return ['id' => $id, 'value' => $entity->internalValue];
		}

		if ($entity instanceof Product) {
			return ['id' => $id, 'value' => $entity->getSuggestName()];
		}

		return ['id' => $id, 'value' => $entity->name];
	}

	private function getRepositoryBySubType(
		string $subType,
		Orm $orm,
	): ProductRepository|LibraryImageRepository|ParameterRepository|ParameterValueRepository|AliasRepository|TreeRepository|SeoLinkLocalizationRepository|ProductTagParentRepository
	{
		return match ($subType) {
			'product' => $orm->product,
			'image' => $orm->libraryImage,
			'parameter' => $orm->parameter,
			'parameterValue' => $orm->parameterValue,
			'alias' => $orm->alias,
			'tree' => $orm->tree,
			'seoLink' => $orm->seoLinkLocalization,
			'productTagParent' => $orm->productTagParent,
			default => throw new \LogicException('Missing \'subType\' in suggest definition'),
		};
	}

	private function processImage(
		mixed $value,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		$repository = $orm->libraryImage;
		$multiple = $this->multiple ?? false;

		if ( ! $multiple) {
			$id = (int) $value;
			if ( ! $id) {
				return null;
			}

			if ( ! $inRs) {
				return $lazyValueFactory->create($repository, $id);
			}

			$image = $repository->getById($id);
			if ($image === null) {
				return null;
			}

			return [['id' => $id, 'src' => $image->getSize('s')->src]];
		}

		if ( ! $inRs) {
			return $repository->findByExactOrder($value);
		}

		$images = [];
		foreach ($value as $id) {
			$image = $repository->getById((int) $id);
			if ($image === null) {
				continue;
			}

			$images[] = ['id' => $id, 'src' => $image->getSize('s')->src];
		}

		return $images;
	}

	private function processFile(
		mixed $value,
		bool $inRs,
		Orm $orm,
		ILazyValueFactory $lazyValueFactory,
	): mixed
	{
		$repository = $orm->file;
		$multiple = $this->multiple ?? false;

		if ( ! $multiple) {
			/** @var \stdClass $value */
			if ( ! isset($value->id)) {
				return null;
			}

			$id = (int) $value->id;
			if ( ! $inRs) {
				$lazyValue = $lazyValueFactory->create($repository, $id);
				$lazyValue->cName = $value->name;
				return $lazyValue;
			}

			$entity = $repository->getById($id);
			if ($entity === null) {
				return null;
			}

			if ($value->name !== $entity->name) {
				$entity->name = $value->name;
				$orm->persistAndFlush($entity);
			}

			return [
				[
					'id' => $id,
					'name' => $entity->name,
					'size' => $entity->sizeMB,
					'type' => $entity->type,
				],
			];
		}

		if ( ! $inRs) {
			$ids = [];
			foreach ($value as $file) {
				$ids[] = $file->id;
			}

			return $repository->findByExactOrder($ids);
		}

		$files = [];
		/** @var \stdClass[] $value */
		foreach ($value as $file) {
			if ( ! isset($file->id)) {
				continue;
			}

			$id = (int) $file->id;
			$entity = $repository->getById($id);
			if ($entity === null) {
				continue;
			}

			if ($file->name !== $entity->name) {
				$entity->name = $file->name;
				$orm->persistAndFlush($entity);
			}

			$files[] = [
				'id' => $entity->id,
				'name' => $entity->name,
				'size' => $entity->sizeMB,
				'type' => $entity->type,
			];
		}

		return $files;
	}

	private function processSelect(
		mixed $value,
		bool $inRs,
	): mixed
	{
		$multiple = $this->multiple ?? false;
		if ( ! $multiple) {
			return $value;
		}

		return $inRs ? $value : \explode(',', $value);
	}
}
