<?php declare(strict_types = 1);

namespace SuperKoderi;

use App\Model\Mutation;
use LogicException;

class MutationHolder
{

	private ?Mutation $mutation = null;

	public function __construct()
	{
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getMutation(): Mutation
	{
		if (!isset($this->mutation)) {
			throw new LogicException('Mutation not set. You must call setMutation() first!');
		}

		return $this->mutation->isAttached() ? $this->mutation : throw new LogicException('Mutation is not attached. You probably called Orm::clear(). Use OrmCleaner::safeClear() instead.');
	}

}
