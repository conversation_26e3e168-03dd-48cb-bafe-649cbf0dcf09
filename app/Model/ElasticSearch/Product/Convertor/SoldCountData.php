<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Product;

class SoldCountData implements Convertor
{

	public function __construct(
	)
	{
	}

	function convert(Product $product): array
	{
		$data = [];
		$data['soldCount'] = $product->soldCount;
		$data['isBestseller'] = (bool)$product->soldCount;

		return $data;
	}
}
