<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;

class DiscountModel
{

	private Orm $orm;

	private Connection $dbal;

	public function __construct(Orm $orm, Connection $dbal)
	{
		$this->orm = $orm;
		$this->dbal = $dbal;
	}


	public function findAllVariantIds(Discount $discount, Mutation $mutation): array
	{
		$variantIdsMerge = [];

		$productIdsByCategories = $this->findProductIdsInMainCategories($discount, $mutation);
		$variantIdsByCategories = $this->orm->productVariant->findBy([
			'product->id' => $productIdsByCategories,
		])->fetchPairs(null, 'id');

		$productIdsByParametersValues = $this->findProductsByParametersValues($discount);

		$variantIdsByParametersValues = $this->orm->productVariant->findBy([
			'product->id' => $productIdsByParametersValues,
		])->fetchPairs(null, 'id');

		if ($variantIdsByCategories !== [] && $variantIdsByParametersValues !== []) {
			$variantIdsMerge = array_intersect($variantIdsByCategories, $variantIdsByParametersValues);
		} elseif ($variantIdsByCategories !== []) {
			$variantIdsMerge = $variantIdsByCategories;
		} elseif ($variantIdsByParametersValues !== []) {
			$variantIdsMerge = $variantIdsByParametersValues;
		}

		$attachedVariantIds = $this->findAttachedVariantIds($discount);
		if ($attachedVariantIds !== []) {
			$variantIdsMerge = array_merge(
				$variantIdsMerge,
				$attachedVariantIds,
			);
		}

		$attachedProductVariantIds = $this->findAttachedProductVariantIds($discount);
		if ($attachedProductVariantIds !== []) {
			$variantIdsMerge = array_merge(
				$variantIdsMerge,
				$attachedProductVariantIds,
			);
		}

		return array_unique($variantIdsMerge);
	}


	private function findProductsByParametersValues(Discount $discount): array
	{
		return $this->orm->product->findByParameterValuesUnion($discount)->fetchPairs(null, 'id');
	}


	private function findProductIdsInMainCategories(Discount $discount, Mutation $mutation): array
	{
		$where = $this->orm->tree->getPublicOnlyWhereParams();

		$where['rootId'] = $mutation->rootId;
		$where['type'] = Tree::TYPE_CATALOG;

		$possibleCatIds = $this->orm->tree->findBy($where)->fetchPairs(null, 'id');
		$selectedCatIds = $discount->categories->toCollection()->findBy($where)->fetchPairs(null, 'id');

		if ($selectedCatIds !== []) {
			// find public main categories
			$results = $this->dbal->query('SELECT pt1.productId FROM product_tree AS pt1
			left JOIN product_tree AS pt2 ON (pt1.productId = pt2.productId and pt2.treeId IN %i[] AND pt1.sort > pt2.sort)
			WHERE pt1.treeId IN %i[] AND pt2.id IS null', $possibleCatIds, $selectedCatIds);

			return $results->fetchPairs(null, 'productId');

		} else {
			return [];
		}
	}


	public function recalculateDiscountPrices(Discount $discount, ?ProductVariant $variant = null): void
	{
		if ($variant !== null) {
			$discountPrices = $discount->discountPrices->toCollection()->findBy([
				'productVariantPrice->productVariant' => $variant,
			]);
		} else {
			$discountPrices = $discount->discountPrices;
		}

		foreach ($discountPrices as $discountPrice) {
			$discountPrice->updatedAt = null;

			$this->orm->discountPrice->persist($discountPrice);
		}

		foreach ($discount->mutations as $mutation) {

			$variantIds = $discount->findAllDiscountedVariantIds($mutation);

			if ($variant !== null && in_array($variant->id, $variantIds, true)) {
				$variantIds = [$variant->id];
			}

			if ($variantIds !== []) {
				foreach ($discount->priceLevels as $priceLevel) {
					$originalPrices = $this->orm->productVariantPrice->findBy([
						'mutation' => $mutation,
						'priceLevel' => $priceLevel,
						'productVariant' => $variantIds,
					]);

					foreach ($originalPrices as $originalPrice) {
						if ($originalPrice->price > 0) {
							$discountPrice = $originalPrice->discountPrices->toCollection()->getBy([
								'discount' => $discount,
							]);

							if ($discountPrice === null) {
								$discountPrice = new DiscountPrice();
								$this->orm->discountPrice->attach($discountPrice);
							}

							$discountPrice->price = round($originalPrice->price * (100 - $discount->discount) / 100, ProductVariantPrice::PRICE_ROUND_PRECISION);
							$discountPrice->discount = $discount;
							$discountPrice->updatedAt = new DateTimeImmutable();
							$discountPrice->productVariantPrice = $originalPrice;

							$this->orm->discountPrice->persist($discountPrice);
						}
					}
				}
			}
		}

		if ($variant !== null) {
			$discountPricesToRemove = $discount->discountPrices->toCollection()->findBy([
				'updatedAt' => null,
				'productVariantPrice->productVariant' => $variant,
			]);
		} else {
			$discountPricesToRemove = $discount->discountPrices->toCollection()->findBy(['updatedAt' => null]);
		}

		foreach ($discountPricesToRemove as $discountPriceToRemove) {
			$this->orm->discountPrice->remove($discountPriceToRemove);
		}

		$this->orm->discountPrice->flush();
	}


	public function recalculateDiscountForProduct(Product $product): void
	{
		$discounts = $this->orm->discount->findBy([
			'publicTo>' => new DateTimeImmutable(),
			'public' => 1,
		]);

		foreach ($discounts as $discount) {
			foreach ($product->variants as $variant) {
				$this->recalculateDiscountPrices($discount, $variant);
			}
		}
	}


	private function findAttachedVariantIds(Discount $discount): array
	{
		return $discount->variants->toCollection()->fetchPairs(null, 'id');
	}


	private function findAttachedProductVariantIds(Discount $discount): array
	{
		$allVariantsFromProducts = [];

		if ($discount->products->count() !== 0) {
			foreach ($discount->products as $product) {
				foreach ($product->activeVariants as $activeVariant) {
					$allVariantsFromProducts[] = $activeVariant->id;
				}
			}
		}

		return $allVariantsFromProducts;
	}


	public function createInfosDiscountPrice(Discount $discount, ProductVariant $productVariant, int $discountPriceValue): void
	{

		$mutation = $this->orm->mutation->getDefault();
		$priceLevel = $this->orm->priceLevel->getDefault();

		$productVariantPrice = null;
		if (isset($productVariant->pricesByLevel[$mutation->id][$priceLevel->id])) {
			$productVariantPrice = $productVariant->pricesByLevel[$mutation->id][$priceLevel->id];
		}

		if ($productVariantPrice === null) {
			throw new \LogicException(sprintf('Missing price for productVariant id %d', $productVariant->id));
		}


		$discountPrice = $this->orm->discountPrice->getBy([
			'productVariantPrice->productVariant' => $productVariant,
			'discount' => $discount,
		]);

		if ($discountPrice === null) {
			$discountPrice = new DiscountPrice();
			$this->orm->discountPrice->attach($discountPrice);
			$discount->variants->add($productVariant);


			$discountPrice->productVariantPrice = $productVariantPrice;
		} else {
			$discountPrice->productVariantPrice = $productVariantPrice;
		}

		$discountPrice->price = $discountPriceValue;
		$discountPrice->discount = $discount;
		$this->orm->persistAndFlush($discountPrice);
	}


	public function removeInfosDiscountPrice(Discount $discount, ProductVariant $productVariant): void
	{
		$discountPrice = $this->orm->discountPrice->getBy([
			'productVariantPrice->productVariant' => $productVariant,
			'discount' => $discount,
		]);

		if ($discountPrice !== null) {
			$discount->variants->remove($productVariant);
			$this->orm->discountPrice->removeAndFlush($discountPrice);
			$this->orm->discount->persistAndFlush($discount);
		}
	}

}
