{var $props = [
	title: 'Kate<PERSON>ie',
	id: 'categories',
	icon: $templates.'/part/icons/folder.svg',
	open: true,
	variant: 'main',
	classes: ['u-mb-xxs'],
	tags: [
		[
			text: 'Lokalizované'
		]
	]
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{foreach $form['localizations']->components as $mutationId=>$localizationContainer}

			{var $mutation = $mutations->getById($mutationId)}
			{var $langCode = $mutation->langCode}

			{var $items = []}

			{foreach $localizationContainer['categories']->components as $categoryId=>$categoryContainer}

				{continueIf $categoryId === 'newItemMarker'}


				{var $url = $urls['searchMutationPage']->toArray()}
				{php $url['params']['mutationId'] = $mutationId}
				{php $url['params']['templates'] = ['Catalog:default']}

				{var $item = [
					data: [
						controller: 'RemoveItem SuggestInp',
						removeitem-target: 'item',
						suggestinp-target: 'wrapper',
						suggestinp-url-value: Nette\Utils\Json::encode($url)

					],
					inps: [
						[
							placeholder: 'Zadejte název kategorie',
							input: $categoryContainer['name'],
							data: [
								suggestinp-target: 'input',
							]
						],
						[
							input: $categoryContainer['id'],
							data: [
								suggestinp-target: 'idInput',
							],
							classes: 'u-hide',
							type: 'hidden'
						]
					],
					btnsAfter: [
						[
							icon: $templates.'/part/icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: [
								action: 'RemoveItem#remove'
							]
						]
					]
				]}

				{php $items[] = $item}
			{/foreach}


			{capture $localizedContent}
				{include $templates.'/part/box/list.latte',
					props: [
						data: [
							controller: 'List',
							List-mutationId-value: $mutation->id,
							List-name-value: 'category',
						],
						listData: [
							List-target: 'list',
						],
						addData: [
							action: 'List#add',
						],
						add: true,
						dragdrop: true,
						items: $items
					]
				}
			{/capture}

			{if $langCode == $defaultMutation->langCode}
				<div class="js-lang js-lang--{$langCode|lower}">
					<div class="u-mb-xxs tag">
						{$langCode|upper}
					</div>
					{$localizedContent}
				</div>
			{else}
				<div class="js-lang js-lang--{$langCode|lower}">
					{include $templates.'/part/core/checkbox.latte',
						props: [
							input: $form['localizations'][$mutation->id]['setup']['inheritCategories'],
							label: '<span class="grid-inline"><span class="tag">'.strtoupper($langCode).'</span> <span>přebírá nastavení z výchozí verze</span></span>',
							dataInp: [
								controller: 'ToggleCheckbox',
								action: 'ToggleCheckbox#changeClass',
								togglecheckbox-target-value: '#checkbox-categories-'.strtolower($langCode),
								togglecheckbox-target-class-value: 'is-open'
							],
						]
					}
					{var $isOpen = !($form['localizations'][$mutation->id]['setup']['inheritCategories']->getValue())}

					<div id="checkbox-categories-{$langCode|lower}" class="js-toggle-checkbox__content u-mt--xs {if $isOpen}is-open{/if}">
						{$localizedContent}
					</div>
				</div>
			{/if}
		{/foreach}

	{/block}
{/embed}












