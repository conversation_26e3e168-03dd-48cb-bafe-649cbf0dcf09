<?php declare(strict_types = 1);

namespace AdminModule;

use App\Model\User;
use App\Model\Voucher;
use Nette\Application\UI\Template;
use Nette\DI\Attributes\Inject;
use SuperKoderi\Admin\Components\Voucher\DataGrid\Grid;
use SuperKoderi\Admin\Components\Voucher\DataGrid\IGridFactory;
use SuperKoderi\Admin\Components\Voucher\Form\Form;
use SuperKoderi\Admin\Components\Voucher\Form\IFormFactory;
use SuperKoderi\Admin\Components\Voucher\Form\IShellFormFactory;
use SuperKoderi\Admin\Components\Voucher\Form\ShellForm;

/**
 * @property Voucher $object
 * @property-read Template $template
 */
class VoucherPresenter extends BasePresenter
{

	#[Inject]
	public IGridFactory $gridFactory;

	#[Inject]
	public IShellFormFactory $shellFormFactory;

	#[Inject]
	public IFormFactory $formFactory;

	#[Inject]
	public \SuperKoderi\Admin\Components\Voucher\CodeGrid\IGridFactory $codeGridFactory;

	private Voucher $voucher;

	public function actionEdit(int $id): void
	{
		$voucher = $this->orm->voucher->getById($id);
		if ($voucher === null) {
			$this->redirect('default');
		}

		$this->voucher = $voucher;
	}

	public function renderEdit(int $id): void
	{
	}


	public function createComponentGrid(): Grid
	{
		return $this->gridFactory->create();
	}


	public function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create($this->voucher, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->shellFormFactory->create($userEntity);
	}



	protected function createComponentCodeGrid(): \SuperKoderi\Admin\Components\Voucher\CodeGrid\Grid
	{
		return $this->codeGridFactory->create($this->voucher);
	}


	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
