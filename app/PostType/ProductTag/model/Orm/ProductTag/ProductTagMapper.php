<?php declare(strict_types = 1);

namespace App\Model;

use App\Tratis\Orm\hasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ProductTagMapper extends DbalMapper
{

	use hasCamelCase;

	protected $tableName = 'product_tag';

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->manyHasManyStorageNamePattern = '%s_%s';

		return $conventions;
	}




	/**
	 * @return ICollection|ProductTag[]
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_  collate utf8mb4_unicode_ci', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}


	public function findFiltered(array $ids): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('productTag', 'p')
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
