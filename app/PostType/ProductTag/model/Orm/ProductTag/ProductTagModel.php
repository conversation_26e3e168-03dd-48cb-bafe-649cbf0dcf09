<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Utils\ArrayHash;

class ProductTagModel
{

	private Orm $orm;

	private ProductTagParentModel $productTagParentModel;

	public function __construct(
		Orm $orm,
		ProductTagParentModel $productTagParentModel
	)
	{
		$this->orm = $orm;
		$this->productTagParentModel = $productTagParentModel;
	}


	public function create(Mutation $mutation, ?ProductTagParent $productTagParent = null): ProductTag
	{
		$productTag = new ProductTag();
		$this->orm->productTag->attach($productTag);
		$productTag->mutation = $mutation;

		if ($productTagParent === null) {
			$productTag->parent = $this->productTagParentModel->create();
		} else {
			$productTag->parent = $productTagParent;
		}

		$this->orm->persistAndFlush($productTag);

		return $productTag;
	}


	public function remove(ProductTag $productTag): void
	{
		$parent = $productTag->parent;
		$this->orm->productTag->removeAndFlush($productTag);

		if ($parent->productTags->count() === 0) {
			$this->productTagParentModel->remove($parent);
		}
	}


	public function copyWithParent(ProductTag $source, ProductTag $target): ProductTag
	{
		$target = $this->copy($source, $target);

		$target->parent->sort = $source->parent->sort;
		$target->parent->customFieldsJson = $source->parent->customFieldsJson;
		$target->parent->customContentJson = $source->parent->customContentJson;
		$target->parent->internalName = sprintf('(copy) %s', $source->parent->internalName);

		return $target;
	}

	public function copy(ProductTag $source, ProductTag $target): ProductTag
	{
		$target->name = sprintf('(copy) %s', $source->name);
		$target->public = 0;
		$target->customFieldsJson = $source->customFieldsJson;
		$target->customContentJson = $source->customContentJson;

		return $target;
	}

	public function localizationExist(ProductTagParent $parent, Mutation $mutation): bool
	{
		return ($parent->productTags->toCollection()->getBy(['mutation' => $mutation]) !== null);
	}
}
