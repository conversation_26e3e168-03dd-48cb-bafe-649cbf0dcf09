<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use SuperKoderi\hasPublicParameterTrait;

/**
 * @method ProductTag getById($id)
 * @method ProductTag[]|ICollection searchByName(string $q, array $excluded = [])
 * @method ProductTag[]|ICollection findFiltered(array $ids)
 */
final class ProductTagRepository extends Repository
{

	use hasPublicParameterTrait;

	public static function getEntityClassNames(): array
	{
		return [ProductTag::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		return $ret;
	}

}
