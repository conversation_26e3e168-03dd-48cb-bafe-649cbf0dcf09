<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\hasCustomContentTrait;
use SuperKoderi\hasCustomFieldTrait;

/**
 * @property int $id {primary}
 * @property string|null $name
 * @property int|null $public
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 *


 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property ProductTagParent $parent {M:1 ProductTagParent::$productTags}
 * @property Mutation $mutation {M:1 Mutation, oneSided=true}

 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 */
class ProductTag extends RoutableEntity
{

	use hasCustomFieldTrait;
	use hasCustomContentTrait;


	protected function getterTemplate(): string
	{
		return 'ProductTag:detail';
	}


	protected function getterPath(): array
	{
		$pathIds = [];
		if (isset($this->mutation->pages->homepage)) {
			$pathIds[] = $this->mutation->pages->homepage->id;
		}

		return $pathIds;
	}

	public function getId(): int
	{
		return $this->id;
	}

	protected function getMutation(): Mutation
	{
		return $this->mutation;
	}
}
