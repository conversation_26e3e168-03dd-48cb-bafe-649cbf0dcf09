<?php declare(strict_types = 1);

namespace App\Model;

use App\Tratis\Orm\hasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ProductTagParentMapper extends DbalMapper
{

	use hasCamelCase;

	protected $tableName = 'product_tag_parent';



	public function findByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalName LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}
}
