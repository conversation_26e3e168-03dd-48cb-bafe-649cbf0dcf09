<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductTagParent getById($id)
 * @method ProductTagParent[]|ICollection findByName(string $q, array $excluded = [])
 */
final class ProductTagParentRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductTagParent::class];
	}

}
