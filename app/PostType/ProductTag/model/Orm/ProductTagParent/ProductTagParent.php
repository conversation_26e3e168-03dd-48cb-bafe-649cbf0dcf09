<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasCustomContentTrait;
use SuperKoderi\hasCustomFieldTrait;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property ProductTag[]|OneHasMany $productTags {1:M ProductTag::$parent, orderBy=[id=ASC]}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property string $color {virtual}
 */
class ProductTagParent extends Entity
{
	use hasCustomFieldTrait;
	use hasCustomContentTrait;

	public function getName(Mutation $mutation): string
	{
		/** @var ProductTag|null $productTag */
		$productTag = $this->productTags->toCollection()->getBy(['mutation' => $mutation]);

		return $productTag?->name ?? $this->internalName;
	}

	public function getterColor(): string
	{
		return $this->cf->type[0]->color ?? '#fff';
	}
}
