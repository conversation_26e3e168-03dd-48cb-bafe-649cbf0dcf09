<?php declare(strict_types = 1);

namespace App\Model;

class ProductTagParentModel
{

	private Orm $orm;

	public function __construct(Orm $orm)
	{
		$this->orm = $orm;
	}


	public function create(): ProductTagParent
	{
		$productTagParent = new ProductTagParent();
		$this->orm->productTagParent->attach($productTagParent);
		$this->orm->persistAndFlush($productTagParent);

		return $productTagParent;
	}


	public function remove(ProductTagParent $productTagParent): void
	{
		$this->orm->productTagParent->removeAndFlush($productTagParent);
	}

}
