parameters:
	config:

		#Register module like in admin.neon style
		modules:
			Admin:ProductTag: ProductTag

		productTag:
			paging: 10

cf:
	definitions:
		color:
			type: list
			label: "Barva"
			items:
				color:
					type: text
					label: "Barva"
		tagContent:
			type: group
			label: "Obsah"
			items:
				content: @cf.definitions.content

	templates:
		"productTagParent":
			type: @cf.definitions.color

		"productTag":
			content: @cf.definitions.tagContent

services:

	-
		implement: SuperKoderi\Admin\Components\ProductTag\Form\IFormFactory
		inject: true

	- SuperKoderi\Admin\Components\ProductTag\DataGrid\IDataGridFactory
	- SuperKoderi\Admin\Components\ProductTag\Form\Model
	- SuperKoderi\Admin\Components\ProductTag\Form\Builder
	- SuperKoderi\Admin\Components\ProductTag\Form\Success
	- App\Model\ProductTagModel
	- App\Model\ProductTagParentModel

	-
		implement: SuperKoderi\Admin\Components\ProductTag\Form\IShellFormFactory
		inject: true

