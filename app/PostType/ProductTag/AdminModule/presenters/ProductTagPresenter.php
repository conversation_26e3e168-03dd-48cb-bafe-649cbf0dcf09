<?php declare(strict_types = 1);

namespace AdminModule;

use App\Model\ProductTag;
use App\Model\User;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use SuperKoderi\Admin\Components\ProductTag\DataGrid\DataGrid;
use SuperKoderi\Admin\Components\ProductTag\DataGrid\IDataGridFactory;
use SuperKoderi\Admin\Components\ProductTag\Form\Form;
use SuperKoderi\Admin\Components\ProductTag\Form\IFormFactory;
use SuperKoderi\Admin\Components\ProductTag\Form\IShellFormFactory;
use SuperKoderi\Admin\Components\ProductTag\Form\ShellForm;

/**
 * @property-read DefaultTemplate $template
 */
class ProductTagPresenter extends BasePresenter
{

	/** @var IDataGridFactory @inject */
	public IDataGridFactory $productTagDataGridFactory;

	/** @var IFormFactory @inject */
	public IFormFactory $productTagFormFactory;

	/** @inject */
	public IShellFormFactory $shellFormFactory;

	private ProductTag $productTag;

	private ?ProductTag $otherProductTag;


	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id, ?int $otherId): void
	{
		$productTag = $this->orm->productTag->getById($id);

		if ($productTag === null) {
			$this->redirect('default');
		}

		$otherProductTag = $productTag->parent->productTags->toCollection()->getById($otherId);
		if ($otherId !== null && $otherProductTag === null) {
			$this->redirect('edit', ['id' => $productTag->id]);
		}

		$this->productTag = $productTag;
		$this->otherProductTag = $otherProductTag;
	}


	public function renderEdit(int $id): void
	{
		$this->template->add('otherProductTag', $this->otherProductTag);
	}


	public function createComponentProductTagGrid(): DataGrid
	{
		return $this->productTagDataGridFactory->create();
	}


	public function createComponentProductTagForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->productTagFormFactory->create($this->productTag, $userEntity);
	}
	public function createComponentOtherProductTagForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->productTagFormFactory->create($this->otherProductTag, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}


	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
