<?php declare(strict_types = 1);

namespace SuperKoderi\Admin\Components\ProductTag\Form;

use App\Model\ProductTag;
use App\Model\ProductTagModel;
use App\Model\Mutation;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\Translator;

class ShellForm extends Control
{

	use hasOrmTrait;
	use hasConfigServiceTrait;


	private Translator $translator;

	private ProductTagModel $productTagModel;

	private ?ProductTag $productTag;

	/** @var ICollection|Mutation[] */
	private ICollection $mutations;

	private array $mutationSelectInputRows;

	public function __construct(
		?ProductTag $productTag,
		Translator $translator,
		ProductTagModel $productTagModel
	)
	{
		$this->translator = $translator;
		$this->productTagModel = $productTagModel;
		$this->productTag = $productTag;

		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$mutationIdToSkip = [];
//		if ($this->productTag !== null) {
//			foreach ($this->productTag->parent->productTags as $productTag) {
//				$mutationIdToSkip[$productTag->mutation->id] = $productTag->mutation->id;
//			}
//		}
		$this->mutations = $this->orm->mutation->findAll();
		$this->mutationSelectInputRows = $this->mutations->fetchPairs('id', 'name');
	}


	public function render(): void
	{

		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);
		$template->add('productTag', $this->productTag);
		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'select_mutation', $this->mutationSelectInputRows);
		$form->addSubmit('send', 'send');


		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$mutation = $this->orm->mutation->getById($values->mutation);

		if ($mutation !== null) {
			if ($this->productTag === null) {
				$newProductTag = $this->productTagModel->create($mutation);
			} else if ($this->productTagModel->localizationExist($this->productTag->parent, $mutation)) {
				$newProductTag = $this->productTagModel->create($mutation);
				$newProductTag = $this->productTagModel->copyWithParent($this->productTag, $newProductTag);
			} else {
				$newProductTag = $this->productTagModel->create($mutation, $this->productTag->parent);
				$newProductTag = $this->productTagModel->copy($this->productTag, $newProductTag);
			}

			$this->orm->persistAndFlush($newProductTag);

			$this->presenter->redirect('edit', ['id' => $newProductTag->id]);
		} else {
			$form->addError('Akce se nezdařila');
			$this->flashMessage('Akce se nezdařila', 'error');
		}
	}

}


interface IShellFormFactory
{
	public function create(?ProductTag $productTag = null): ShellForm;

}
