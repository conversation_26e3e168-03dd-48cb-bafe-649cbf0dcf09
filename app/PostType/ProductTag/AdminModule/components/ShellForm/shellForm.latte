<form n:name="form" n:if="$mutations->count()">

	{embed $templates.'/part/box/std.latte', props=>[

	]}
		{block content}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['mutation'],
				type: 'select',
			]}
			<p>
				<button n:name="send" class="btn btn--full btn--success">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/save.svg'}
					</span>
					<span class="item-icon__text">
						{if $productTag === null}
							Založit
						{else}
							Duplikovat
						{/if}

					</span>
				</span>
				</button>
			</p>

		{/block}
	{/embed}
</form>
