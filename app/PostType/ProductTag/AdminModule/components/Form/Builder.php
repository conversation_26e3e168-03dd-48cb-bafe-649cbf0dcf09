<?php declare(strict_types=1);

namespace SuperKoderi\Admin\Components\ProductTag\Form;

use App\Model\ProductTag;
use App\Model\ProductTagParent;
use Nette\Application\UI\Form;
use Nette\Forms\Container;

class Builder
{
	public function __construct()
	{
	}

	public function build(Form $form, ProductTag $productTag, array $postData): void
	{
		$this->addProductTagToForm($form, $productTag, $postData);
		$this->addParentToForm($form, $productTag->parent, $postData);
		$this->addButtonsCommon($form);
	}


	private function addParentToForm(Form $form, ProductTagParent $productTagParent, array $postData): void
	{
		$container = $form->addContainer('parent');
		$this->addParentCommonToForm($container, $productTagParent, $postData);
	}


	private function addParentCommonToForm(Container $container, ProductTagParent $productTagParent, array $postData): void
	{
		$container->addText('internalName', 'internalName')->setDefaultValue($productTagParent->internalName);
		$container->addText('sort', 'sort')->setDefaultValue($productTagParent->sort);
	}


	private function addProductTagToForm(Form $form, ProductTag $productTag, array $postData): void
	{
		$container = $form->addContainer('localization');
		$this->addCommonToForm($container, $productTag, $postData);
	}


	private function addCommonToForm(Container $container, ProductTag $productTag, array $postData): void
	{
		$container->addText('name', 'name')->setDefaultValue($productTag->name);
		$container->addCheckbox('public', 'public')->setDefaultValue($productTag->public);
	}

	private function addButtonsCommon(Form $form): void
	{
		$form->addSubmit('send');
		$form->addSubmit('copy');
	}



}
