<?php declare(strict_types = 1);

namespace SuperKoderi\Admin\Components\ProductTag\Form;

use App\Model\ProductTag;
use App\Model\ProductTagModel;
use App\Model\ProductTagParent;
use App\Model\Orm;
use App\Model\User;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\CustomContent\CustomContent;
use SuperKoderi\CustomField\CustomFields;

class Success
{
	public function __construct(
		private CustomFields $customFields,
		private CustomContent $customContent,
		private Orm $orm,
	) {}


	public function execute(Form $form, ProductTag $productTag, User $user, ArrayHash $values): void
	{
		$data = $form->getHttpData();

		$this->handleLocalization($productTag, $user, $values->localization, $data);
		$this->handleParent($productTag->parent, $values->parent, $data);

		$this->orm->productTag->persistAndFlush($productTag);
	}

	private function handleParent(ProductTagParent $parent, ArrayHash $parentFormData, array $data): void
	{
		$this->handleParentCommon($parent, $parentFormData);
		$this->handleParentCustomContent($parent, $data);
		$this->handleParentCustomField($parent, $data);
	}


	private function handleLocalization(ProductTag $productTag, User $user, ArrayHash $commonFormData, array $data): void
	{
		$this->handleCommon($productTag, $user, $commonFormData);
		$this->handleCustomContent($productTag, $data);
		$this->handleCustomField($productTag, $data);
	}


	private function handleCommon(ProductTag $productTag, User $userEntity, ArrayHash $commonFormData): void
	{
		$productTag->public = (int) $commonFormData->public;
		$productTag->edited = $userEntity->id;
		$productTag->editedTime = new DateTimeImmutable();
		$productTag->name = $commonFormData->name;
	}


	private function handleParentCommon(ProductTagParent $parent, ArrayHash $parentFormData): void
	{
		$parent->internalName = $parentFormData->internalName;
		$parent->sort = $parentFormData->sort;
	}


	private function handleCustomContent(ProductTag $productTag, array $data): void
	{
		if (isset($data['cc']) && isset($data['ccScheme'])) {
			$productTag->customContentJson = $this->customContent->prepareDataToSave($data['cc'], $data['ccScheme']);
		}
	}


	private function handleCustomField(ProductTag $productTag, array $data): void
	{
		if (isset($data['cf'])) {
			$productTag->cf = $this->customFields->prepareDataToSave($data['cf']);
		}
	}

	private function handleParentCustomContent(ProductTagParent $parent, array $data): void
	{
		if (isset($data['ccParent']) && isset($data['ccParentScheme'])) {
			$parent->customContentJson = $this->customContent->prepareDataToSave($data['ccParent'], $data['ccParentScheme']);
		}
	}


	private function handleParentCustomField(ProductTagParent $parent, array $data): void
	{
		if (isset($data['cfParent'])) {
			$parent->cf = $this->customFields->prepareDataToSave($data['cfParent']);
		}
	}

}
