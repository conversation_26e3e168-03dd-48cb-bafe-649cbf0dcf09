{var $icon = $templates.'/part/icons/grip-vertical.svg'}


{var $props = [
	title: $title,
	id: $itemName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],

]}

{php $headerItems[] = [href: '#'.$itemName, icon: $icon, tooltip: $title, linkType: 'toggle']}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="b-std u-mb-sm">
			<h3 class="b-std__title title"></h3>

			<div class="b-std__content"
				 data-controller="ModularContent CustomFields ToggleAll"
				 data-action="ModularContent:addToScheme->CustomFields#addToScheme CustomFields:updateSchemeValue->ModularContent#updateModules CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
				 data-customfields-lang-value="{$mutation->langCode}"
				 data-customfields-scheme-value="{$ccObject->getCCSchemeJson()}"
				 data-customfields-values-value='{$ccObject->getCCJson()}'
				 data-customfields-uploadurl-value="{$fileUploadLink}"
				 data-customfields-mutationid-value="{$mutation->id}"
				 data-modularcontent-modules-value='{$ccObject->getCCModulesJson()}'
			>
				<p><button type="button" class="btn" data-action="ToggleAll#toggle"><span class="btn__text">Sbalit/rozbalit vše</span></button></p>
				<div data-customfields-target="content"></div>
				<input type="hidden" data-customfields-target="values" name="{$itemName}">
				<input type="hidden" data-modularcontent-target="modules" name="{$itemName}Scheme">
				<div class="m-icons c-custom-fields__menu u-mb-sm">
					<ul class="m-icons__list m-icons--wrap" data-modularcontent-target="menu"></ul>
					<div class="cc-search inp u-hide" data-modularcontent-target="search">
						<div class="inp-fix">
							<input class="inp-text" type="text" placeholder="Piš pro filtrování" data-action="input->ModularContent#filterCategories" data-modularcontent-target="searchinput">
							<div class="inp-fix__sufix"><a href="#" data-action="ModularContent#toggleSearch"><span class="ico ico--times"></span></a></div>
							<div class="inp-text__holder"></div>
							<div class="b-suggest is-visible" data-modularcontent-target="suggest"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/block}
{/embed}

