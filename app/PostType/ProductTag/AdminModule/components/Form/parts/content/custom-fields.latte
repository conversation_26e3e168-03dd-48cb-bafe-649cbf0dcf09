{var $icon = $templates.'/part/icons/list.svg'}

{var $props = [
	title: $title,
	id: $itemName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}

{php $headerItems[] = [href: '#'.$itemName, icon: $icon, tooltip: $title, linkType: 'toggle']}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="b-std u-mb-sm">
			<h3 class="b-std__title title"></h3>
			<div class="b-std__content"
				data-controller="CustomFields"
				data-action="CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
				data-customfields-scheme-value="{$cfObject->getCfSchemeJson()}"
				data-customfields-values-value="{$cfObject->getCfContent()}"
				data-customfields-uploadurl-value="{$fileUploadLink}"
			>
				<div data-customfields-target="content"></div>
				<input type="hidden" data-customfields-target="values" name="{$itemName}">
			</div>
		</div>
	{/block}
{/embed}
