<?php declare(strict_types = 1);

namespace SuperKoderi\Admin\Components\ProductTag\Form;

use App\Model\ProductTag;
use App\Model\ProductTagModel;
use App\Model\Mutation;
use App\Model\User;
use Nette\Application\UI\Control;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\CustomField\SuggestUrls;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\LinkFactory;
use SuperKoderi\Translator;
use SuperKoderi\TranslatorDB;

class Form extends Control
{

	use hasOrmTrait;
	use hasConfigServiceTrait;

	/** @var array */
	private array $postData;

	/** @var ProductTag */
	private ProductTag $productTag;

	/** @var User */
	private User $userEntity;

	private SuggestUrls $urls;

	/** @var LinkFactory */
	private LinkFactory $linkFactory;

	/** @var Translator */
	private Translator $translator;

	/** @var TranslatorDB */
	private TranslatorDB $translatorDB;


	/** @var Builder */
	private Builder $formBuilder;

	/** @var Success */
	private Success $formSuccess;

	/** @var Mutation[]|ICollection */
	private $mutations;

	private IShellFormFactory $shellFormFactory;

	private ProductTagModel $productTagModel;

	public function __construct(
		ProductTag $productTag,
		User $userEntity,
		SuggestUrls $urls,
		LinkFactory $linkFactory,
		Translator $translator,
		TranslatorDB $translatorDB,
		IShellFormFactory $shellFormFactory,
		ProductTagModel $productTagModel,
		Builder $formBuilder,
		Success $formSuccess
	)
	{
		$this->productTag = $productTag;
		$this->userEntity = $userEntity;
		$this->urls = $urls;
		$this->linkFactory = $linkFactory;
		$this->translator = $translator;
		$this->translatorDB = $translatorDB;
		$this->formBuilder = $formBuilder;
		$this->formSuccess = $formSuccess;

		$this->onAnchor[] = [$this, 'init'];
		$this->shellFormFactory = $shellFormFactory;
		$this->productTagModel = $productTagModel;
	}


	public function init(): void
	{
		$method = $this->getPresenter()->request->getMethod();

		if ($method === IRequest::POST) {
			$this->postData = $this->getPresenter()->request->getPost();
		} else {
			$this->postData = [];
		}

		$this->mutations = $this->orm->mutation->findAll();
	}


	public function render(array $args = []): void
	{

		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->headerItems = new \ArrayIterator();
		$template->productTag = $this->productTag;
		$template->mutation = $this->productTag->mutation;
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link('File:upload');
		if ($this->productTag->alias) {
			$template->linkToFront = $this->linkFactory->linkTranslateToNette($this->productTag, ['show' => 1, 'mutation' => $this->productTag->mutation]);
		} else {
			$template->linkToFront = null;
		}

		$template->otherProductTags = $this->productTag->parent->productTags->toCollection()
			->findBy(['mutation!=' => $this->productTag->mutation])
			->resetOrderBy();

		$activeMutationLangCodes = [];
		foreach ($this->productTag->parent->productTags
					 ->toCollection()
					 ->resetOrderBy() as $productTag) {
			$activeMutationLangCodes[] = $productTag->mutation->langCode;
		}



		$missingMutations = $this->mutations->findBy(['langCode!=' => $activeMutationLangCodes]);


		$this->template->missingMutations = $missingMutations;
		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;


		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setTranslator($this->translator);

		$this->formBuilder->build($form, $this->productTag, $this->postData);

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(\Nette\Application\UI\Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(\Nette\Application\UI\Form $form, ArrayHash $values): void
	{
		$this->formSuccess->execute($form, $this->productTag, $this->userEntity, $values);
		$productTag = $this->productTag;
		$this->presenter->redirect('edit', ['id' => $productTag->id]);
	}


	public function handleDelete(): void
	{
		$this->productTagModel->remove($this->productTag);
		$this->presenter->redirect('default');
	}


	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create($this->productTag);
	}

}


interface IFormFactory
{

	/**
	 * @return Form
	 */
	function create(ProductTag $productTag, User $userEntity);

}
