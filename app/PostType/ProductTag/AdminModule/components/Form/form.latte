{varType App\Model\ProductTag $productTag}
{var $langView = isset($otherProductTag) && $otherProductTag}

<form n:name="form" class="main__main{if $langView} main__main--lang{/if}">

	<div class="main__content scroll">
		{include './parts/content/content.latte', form=>$form}

		{if $productTag->parent->getCfScheme()}
			{include './parts/content/custom-fields.latte',
			form=>$form,
			cfObject=>$productTag->parent,
			title=>'Vlastní pole',
			itemName=>'cfParent'}
		{/if}

		{if $productTag->parent->getCCModules()}
			{include './parts/content/custom-content.latte',
			form=>$form,
			ccObject=>$productTag->parent,
			title=>'Modulární obsah',
			mutation=>$productTag->mutation,
			itemName=>'ccParent'}
		{/if}

		{if $productTag->getCfScheme()}
			{include './parts/content/custom-fields.latte',
			form=>$form,
			cfObject=>$productTag,
			title=>'Jazykově závislé informace',
			itemName=>'cf'}
		{/if}

		{if $productTag->getCCModules()}
			{include './parts/content/custom-content.latte',
			form=>$form,
			ccObject=>$productTag,
			title=>'Jazykově závislý modulární obsah',
			mutation=>$productTag->mutation,
			itemName=>'cc'}
		{/if}
	</div>

	<div class="main__header">
		{include './parts/header.latte', productTag=>$productTag}
	</div>

	{if !$langView}
		<div class="main__content-side scroll">
			{include './parts/side/btns.latte'}
			{include './parts/side/state.latte', form=>$form}
			{include './parts/side/mutations.latte'}
			{include './parts/side/copy.latte'}
			{*{include './parts/side/template.latte', form=>$form}*}
			{include './parts/side/edits.latte'}
		</div>

		{capture $templateTargets}
			{include './parts/newItemTemplate.latte', form=>$form}
			{include $templates . '/part/core/libraryOverlay.latte'}
		{/capture}
	{/if}
</form>

{if !$langView}
	{embed $templates . '/part/core/overlay.latte', props: [
		id: 'addLang',
		title: 'Vytvoření jazykové mutace',
		classes: ['b-overlay--full']
	], templates=>$templates}
		{block content}
			{control shellForm}
		{/block}
	{/embed}

	{$templateTargets|noescape}
{/if}
