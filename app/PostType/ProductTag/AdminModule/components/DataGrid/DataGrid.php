<?php declare(strict_types=1);

namespace SuperKoderi\Admin\Components\ProductTag\DataGrid;

use App\Model\Orm;
use App\Model\Traits\DataGrid\hasColumnMutationTrait;
use Nette\Application\UI\Control;
use SuperKoderi\Translator;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{
	use hasColumnMutationTrait;

	private Orm $orm;

	private Translator $translator;

	public function __construct(
		Translator $translator,
		Orm $orm
	)
	{
		$this->orm = $orm;
		$this->translator = $translator;
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->orm->productTag->findAll()->orderBy('parent->sort'));
		$grid->setDefaultPerPage(50);

		$grid->addColumnText('internalName', 'internalName', 'parent.internalName')->setFilterText();
		$grid->addColumnText('sort', 'sort', 'parent.sort');
		$grid->addColumnText('name', 'name')->setFilterText();
		$this->addColumnMutation($grid);
		$grid->addAction('edit', 'Edit', 'ProductTag:edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}


interface IDataGridFactory
{
	function create(): DataGrid;
}
