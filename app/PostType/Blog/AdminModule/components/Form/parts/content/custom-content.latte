{var $props = [
	title: 'Vlastní obsah',
	id: 'custom-content',
	variant: 'main',
	icon: $templates.'/part/icons/grip-vertical.svg',
	classes: ['u-mb-xxs'],

]}

{varType App\Model\Blog $blog}
{var $mutation = $blog->mutation}
{var $langCode = $mutation->langCode}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="b-std u-mb-sm">
			<h3 class="b-std__title title"></h3>

			<div class="b-std__content"
				 data-controller="ModularContent CustomFields ToggleAll"
				 data-action="ModularContent:addToScheme->CustomFields#addToScheme CustomFields:updateSchemeValue->ModularContent#updateModules CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
				 data-customfields-lang-value="{$langCode}"
				 data-customfields-scheme-value="{$blog->getCCSchemeJson()}"
				 data-customfields-values-value='{$blog->getCCJson()}'
				 data-customfields-uploadurl-value="{$fileUploadLink}"
				 data-customfields-mutationid-value="{$mutation->id}"
				 data-modularcontent-modules-value='{$blog->getCCModulesJson()}'
			>
				<p><button type="button" class="btn" data-action="ToggleAll#toggle"><span class="btn__text">Sbalit/rozbalit vše</span></button></p>
				<div data-customfields-target="content"></div>
				<input type="hidden" data-customfields-target="values" name="customContent">
				<input type="hidden" data-modularcontent-target="modules" name="customContentScheme">
				<div class="m-icons c-custom-fields__menu u-mb-sm">
					<ul class="m-icons__list" data-modularcontent-target="menu"></ul>
					<div class="cc-search inp u-hide" data-modularcontent-target="search">
						<div class="inp-fix">
							<input class="inp-text" type="text" placeholder="Piš pro filtrování" data-action="input->ModularContent#filterCategories" data-modularcontent-target="searchinput">
							<div class="inp-fix__sufix"><a href="#" data-action="ModularContent#toggleSearch"><span class="ico ico--times"></span></a></div>
							<div class="inp-text__holder"></div>
							<div class="b-suggest is-visible" data-modularcontent-target="suggest"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/block}
{/embed}

