<?php declare(strict_types = 1);

namespace App\Console;

use Exception;
use SuperKoderi\ConfigService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class FooCommand extends Command
{

	use LockableTrait;

	public function __construct()
	{
		parent::__construct(null);
	}


	protected static $defaultName = 'app:foo';

	protected function configure(): void
	{
		$this->setDescription('Command template');
	}

	/**
	 * @throws Exception
	 */
	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		if (!$this->lock()) {
			throw new Exception('The command is already running in another process.');
		}

		$this->runCommand($output);
		$output->writeln('DONE');

		return 0;
	}

	private function runCommand(OutputInterface $output): void
	{
		$output->writeln('Hello world');
	}

}
