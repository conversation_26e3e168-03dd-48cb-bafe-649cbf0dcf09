<?php declare(strict_types = 1);

namespace SuperKoderi\Console\Model;

use App\Model\Orm;
use SuperKoderi\ConfigService;

class Robots
{
	private Orm $orm;

	private ConfigService $configService;

	public function __construct(Orm $orm, ConfigService $configService)
	{
		$this->orm = $orm;
		$this->configService = $configService;
	}

	public function write(): void
	{
		$fileTarget = WWW_DIR . '/robots.txt';

		$content = [];

		$rows = $this->configService->get('robotsTxt', 'rows');
		foreach ($rows as $row) {
			$content[] = $row;
		}

		if ($this->configService->get('bucketFilter', 'flags') !== null) {
			$content[] = '';
			foreach ($this->configService->get('bucketFilter', 'flags') as $name) {
				$content[] = 'Disallow: /*?' . $name;
				$content[] = 'Disallow: /*&' . $name;
			}
		}


		if ($this->configService->get('bucketFilter', 'flagValues') !== null) {
			$content[] = '';
			foreach ($this->configService->get('bucketFilter', 'flagValues') as $name) {
				$content[] = 'Disallow: /*?' . $name;
				$content[] = 'Disallow: /*&' . $name;
			}
		}


		if ($this->configService->get('bucketFilter', 'ranges') !== null) {
			$content[] = '';
			foreach ($this->configService->get('bucketFilter', 'ranges') as $name) {
				$content[] = 'Disallow: /*?' . $name;
				$content[] = 'Disallow: /*&' . $name;
			}
		}


		$parameters = $this->orm->parameter->findBy([
			'isInFilter' => 1
		]);
		if ($parameters->count() > 0) {
			$content[] = '';
			foreach ($parameters as $parameter) {
				if ($parameter->uid !== null) {
					$content[] = 'Disallow: /*?' . $parameter->uid;
					$content[] = 'Disallow: /*&' . $parameter->uid;
				}
			}
		}

		file_put_contents($fileTarget, implode(PHP_EOL, $content));
	}

}
