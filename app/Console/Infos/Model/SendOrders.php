<?php

namespace App\Console\Infos\Model;

use App\Model\Erp\MssqlCaller;
use App\Model\Order;
use App\Model\OrderItem;
use App\Model\OrderRepository;
use App\Model\ProductRepository;
use App\Model\UserRepository;
use Nette\Utils\Json;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\MoneyHelper;

class SendOrders
{
	public function __construct(
		private readonly OrderRepository $orderRepository,
		private readonly UserRepository $userRepository,
		private readonly ProductRepository $productRepository,
		private readonly Connection $dbal,
		private readonly MssqlCaller $mssqlCaller,
	)
	{

	}


	public function sendAllNew(): int
	{
		$this->productRepository->setPublicOnly(false);

		// TODO nové ktere nejsou kartou nebo nove zaplacene karou
		$orders = $this->orderRepository->findBy([
			'infosStatus!=' => Order::INFOS_STATUS_OK,
		])->limitBy(5);

		$counter = 0;
		foreach ($orders as $order) {
			$infosOrderRequestArray = $this->buildInfosOrder($order);
			$result = $this->sendToInfos($order, $infosOrderRequestArray);
			if ($result) {
				$counter++;
			}
		}

		$this->productRepository->setPublicOnly(true);

		return $counter;
	}

	public function sendToInfos(Order $order, array $infosOrderRequestArray): bool
	{
		$response = $this->mssqlCaller->callRemoteQueryFromBuilder($infosOrderRequestArray);
		if (isset($response[0]) && $msResponse = $response[0]) {
			if (!isset($msResponse->CisDokladu) || $msResponse->CisDokladu == 0) {
				return false;
			} else {
				$this->updateInfosResponse($order, $msResponse);
				return true;
			}
		}
		return false;
		//$sql = iconv('WINDOWS-1252', 'UTF-8', $sql);

		// todo Execute SQL
		// $result from infos .. TODO
		// save to $order
//		$response = $this->msDb->query(
//			...$infosOrderRequestArray
//		)->fetch();
	}


	public function buildInfosOrder(Order $order): array
	{
		// $this->myDb->query('SET NAMES CP1250');
		$this->dbal->query('SET NAMES CP1250');

		$infosKody = [];
		$infosSarze = [];
		$infosMnozstvi = [];
		$infosSleva = [];



		$infosOrderRequest = '';

		foreach ($order->items as $item) {
			if ($item->product !== null) {
				$infosKody[] = trim($item->infosCode);

				if ($item->type === OrderItem::TYPE_SET) {
					$infosSarze[] = '1';
				} else {
					$infosSarze[] = $this->getRealSarzeFromMsSQL($item->infosCode, $item->infosSarze, $item->amount);
				}
				$infosMnozstvi[] = $item->amount;

				if ($item->inAction) {
					$infosSleva[] = 0;
				} else {
					if ($order->user === null) {
						$infosSleva[] = 0;
					} else {
						$infosSleva[] = $order->user->priceLevel->cleanLevel;
					}
				}
			}
		}

		$notes = [];
		if ($order->infotext) {
			$notes[] = $order->infotext;
		}
		if ($order->dInfo) {
			$notes[] = $order->dInfo;
		}

		$kodySP = implode(';', $infosKody);// – povinné, kódy skladových položek oddělené středníkem
		$mnozstviSP = implode(';', $infosMnozstvi);// – povinné, množství skladových položek oddělené středníkem
		$sarzeSP = implode(';', $infosSarze);// – povinné, šarže skladových položek oddělené středníkem
		$slevy = implode(';', $infosSleva);// – povinné, šarže skladových položek oddělené středníkem

		$kodOdber = '';// – nepovinné, pokud je znám kód odběratele podle IS INFOS
		$infoSkladka = '';// – nepovinná informace varchar(30)
		$poznamka = implode(';', $notes);// – nepovinná informace pro dodavatele
//				$ukazSarze = 1;// – vždy = 1

//			Parametry fakturačního místa pro případ, že není uveden parametr KodOdber
		$FM_ICO = $order->ic;// – nepovinné IČO
		$FM_DIC = $order->dic;// – nepovinné DIČ
		$FM_Adresa1 = $order->name;// – první řádek  adresy nového FM varchar(35)
		$FM_Adresa2 = $order->company;// –druhý řádek  adresy
		$FM_Ulice = $order->street;// - ulice adresy
		$FM_Misto = $order->city;// - místo adresy
		$FM_PSC = $order->zip;// - PSČ char(5)
		$FM_Telefon = $order->phone;// – telefon char(25)
//				$FM_Mobil = 'xx';// – mobilní telefon char(25)

//			Parametry odběrného místa pro případ, že není uveden parametr KodOdber
		$OM_Adresa1 = $order->dName;// – první řádek  adresy nového FM varchar(35)
		$OM_Adresa2 = $order->dCompany;// –druhý řádek  adresy
		$OM_Ulice = $order->dStreet;// - ulice adresy
		$OM_Misto = $order->dCity;// - místo adresy
		$OM_PSC = $order->dZip;// - PSČ char(5)
//				$OM_Telefon = '';// – telefon char(25)
//				$OM_Mobil = '';// – mobilní telefon char(25)

		$PocCisloCR = '';// – nepovinné počáteční číslo číselné řady pro nového odběratele


//				@FM_eMail (e-mail fakturačního místa)
//				@OM_eMail (e-mail odběrného místa)



		$query = [
			"AEI_Objednavka_VIR @zaokrouhlitCeny=1"
			, ",@KodySP = %s", $kodySP
			, ",@MnozstviSP = %s", $mnozstviSP
			, ",@slevy = %s", $slevy
			, ",@SarzeSP = %s", $sarzeSP
			, ",@CisObjednavky = %s", $order->number
//					    , ",@KodOdber = %s", $xxx
			, ",@InfoSkladka = %s", $infoSkladka
			, ",@Poznamka = %s", $poznamka
			, ",@ukazSarze = 1"

			, ",@FM_ICO = %s", $FM_ICO
			, ",@FM_DIC = %s", $FM_DIC
			, ",@FM_Adresa1 = %s", $FM_Adresa1
			, ",@FM_Adresa2 = %s", $FM_Adresa2
			, ",@FM_Ulice = %s", $FM_Ulice
			, ",@FM_Misto = %s", $FM_Misto
			, ",@FM_PSC = %s", $FM_PSC
			, ",@FM_Telefon = %s", $FM_Telefon
//					    , ",@FM_Mobil = %s", $FM_Mobil

			, ",@OM_Adresa1 = %s", $OM_Adresa1
			, ",@OM_Adresa2 = %s", $OM_Adresa2
			, ",@OM_Ulice = %s", $OM_Ulice
			, ",@OM_Misto = %s", $OM_Misto
			, ",@OM_PSC = %s", $OM_PSC
//					    , ",@OM_Telefon = %s", $OM_Telefon
//					    , ",@OM_Mobil = %s", $OM_Mobil

		];

		$user = $order->user;
		if ($user !== null && $user->infosCode) {
			array_push($query, ', @KodOdber = %s', $user->infosCode);
		}


		array_push($query, ', @VariabilniSymbol = %s', $order->number);
//				array_push($query, ', @CisObjPartnera = %s', $order->number);
		array_push($query, ', @FM_eMail = %s', $order->email);
		array_push($query, ', @OM_eMail = %s', $order->email);

		// transport
		// personal 2002, toptrans 2003, messenger 2004

		if (($transport = $order->transport) !== null) {
			if ($transport->subType == Order::TRANSPORT_PERSONAL) {
				array_push($query, ', @DopravaKodSP = %s', 2002);

			} elseif ($transport->subType == Order::TRANSPORT_PERSONAL_OSTRAVA) {
				array_push($query, ', @DopravaKodSP = %s', 2005);

			} elseif ($transport->subType == 'toptrans') {
				array_push($query, ', @DopravaKodSP = %s', 2003);

			} elseif ($transport->subType == Order::TRANSPORT_MESSENGER) {
				array_push($query, ', @DopravaKodSP = %s', 2004);
			}
			array_push($query, ', @DopravaCena = %i', $order->transport->unitPriceDPH);
			array_push($query, ', @DopravaDPH = %f', $order->transport->unitPriceDPH - MoneyHelper::getPriceWithoutVat($order->transport->unitPriceDPH, 21));
		}



		// paymnet
		if (($payment = $order->payment) !== null) {
			if ($payment->subType == 'cash') {
				array_push($query, ', @PlatbaKodSP = %s', 2103);
			} elseif ($payment->subType == 'onDelivery') {
				array_push($query, ', @PlatbaKodSP = %s', 2102);
			} elseif ($payment->subType == 'bank') {
				array_push($query, ', @PlatbaKodSP = %s', 2100);
			} elseif ($payment->subType == 'card') {
				array_push($query, ', @PlatbaKodSP = %s', 2101);
			}

			array_push($query, ', @PlatbaCena = %i', $payment->unitPriceDPH);
			array_push($query, ', @PlatbaDPH = %f', $payment->unitPriceDPH - MoneyHelper::getPriceWithoutVat($payment->unitPriceDPH, 21));


			if ($payment->subType == 'card') {
				array_push($query, ', @IDPlatby = %i', $order->id);
			}
		}

		return $query;

	}



	public function getRealSarzeFromMsSQL(string $code, string $sarze, int $amount): string
	{


		$args = [
			"aei_seznampolozek @kodprovozu = '10'"
			, ", @UkazSarze = 1"
			, ", @kodysp = %s", $code

		];
		$rows = $this->mssqlCaller->callRemoteQueryFromBuilder($args);


		$posibleToReturn = '';
		foreach ($rows as $row) {
			if (preg_match('/' . preg_quote($sarze, '/') . '$/', trim($row->SarzeSP))) {

				// ano nasel sarzi ktera by se dala pouzit
				$posibleToReturn = trim($row->SarzeSP);

				// kontrola na mnozstvi
				if ($row->MnozstviKDispozici >= $amount) {
					// pokud toho je na sklade vic nez je v objednavce
					return trim($row->SarzeSP);
				}
			}
		}

		if ($posibleToReturn) {
			// pokud neni uspokojena objednavka stavem skladu na bzenci
			// pouzij posledni nalezenou sarzi
			return $posibleToReturn;
		} else {
			return $sarze;
		}
	}



	public function updateInfosResponse(Order $order, \stdClass $msResponse): void
	{
		$user =  $order->user;
		$this->userRepository->persistAndFlush($user);

		$order->infosStatus = Order::INFOS_STATUS_OK;
		$order->infosNumber = $msResponse->CisDokladu;
		$order->infosCreatedTime = new DateTimeImmutable();
		$order->infosResponse = Json::encode($msResponse);

		$this->orderRepository->persistAndFlush($order);

	}
}
