import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';
import { handleSnippetResponse } from '../tools/utils';

export default class LibrarySearch extends Controller {
	connect() {
		useDispatch(this);
		this.dispatch('searchEnd');
	}

	clear(event) {
		event.preventDefault();
		this.dispatch('searchStart');
		$.ajax({
			url: event.currentTarget.href,
			type: 'GET',
		}).done((response) => {
			handleSnippetResponse(response);
		});
	}

	submitSearch(event) {
		const data = new FormData(event.currentTarget);
		event.preventDefault();
		this.dispatch('searchStart');

		$.ajax({
			url: event.currentTarget.action,
			type: 'POST',
			data,
			processData: false,
			contentType: false,
		}).done((response) => {
			handleSnippetResponse(response);
		});
	}
}
