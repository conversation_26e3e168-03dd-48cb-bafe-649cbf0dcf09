@use 'config';
@use 'base/variables';

.flags {
	&__list {
		display: flex;
		gap: 5px;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		margin: 0;
		list-style: none;
	}

	&__item {
		display: flex;
		flex-shrink: 0;
		justify-content: center;
		align-items: center;
		margin: 0;
		padding: 0;
		background: none;
		span {
			padding: 2px 6px;
			border-radius: 4px;
			font-weight: 300;
			font-size: 12px;
		}
	}

	&__discount-value {
		position: relative;
		z-index: 1;
		width: 48px;
		height: 40px;
		margin-right: -8px;
		padding-top: 6px;
		font-weight: bold;
		font-size: 16px;
		text-align: right;

		&::before {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			z-index: -1;
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background-color: variables.$color-sg-green;
		}
	}

	.icon-svg {
		max-width: 40px;
		max-height: 40px;
	}
	&--series {
		position: absolute;
		top: 10px;
		left: 40px;
		width: 150px;
		max-width: 27%;

		img {
			max-width: 100%;
			height: auto;
		}
	}

	&--favorite {
		position: absolute;
		top: -10px;
		right: 0;
	}

	&--main-labels {
		position: absolute;
		top: 30px;
		right: 0;
	}

	@media (config.$md-down) {
		&--series {
			left: 0;
		}
	}
}
