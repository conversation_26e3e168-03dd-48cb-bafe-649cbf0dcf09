@use 'config';
@use 'base/variables';
@use 'base/extends';

.m-main {
	$s: &;
	&--component {
		#{$s}__link {
			font-weight: 700;
		}
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-direction: column;
		margin: 0;
	}
	&__item {
		@extend %reset-ul-li;
		margin: 0;
		padding: 0;
		background: none;
		transition: background-color variables.$t;

		&.is-active > #{$s}__link,
		.hoverevents &:hover #{$s}__link {
			color: variables.$color-secondary;
		}
	}

	&__link {
		display: flex;
		align-items: center;
		padding: 7px;
		color: variables.$color-primary-inverse;
		font-size: 16px;
		text-decoration: none;

		// .icon-svg {
		// 	margin-right: 5px;
		// 	color: currentcolor;
		// 	transform: translateY(-2px);
		// 	fill: currentcolor;
		// }
	}

	&__link-icon {
		display: none;
		width: 10px;
	}

	&__btn {
		border: none;
		background-color: transparent;
		color: inherit;

		.icon-svg {
			width: 10px;
			transform: rotateZ(90deg);
			transition: transform variables.$t ease;
		}

		.is-open > & {
			.icon-svg {
				transform: rotateZ(-90deg);
			}
		}

		.hoverevents &:hover,
		&:focus-visible {
			color: variables.$color-secondary;
		}
	}
	@media (config.$lg-down) {
		&__item {
			position: relative;

			& + #{$s}__item {
				border-top: 1px solid rgba(#ffffff, 0.2);
			}
		}
		&__link {
			display: block;
			min-height: 48px;
			padding: 10px 15px;

			.has-submenu > &:not(.no-link) {
				margin-right: 60px;
			}

			&.is-active,
			&.is-selected {
				color: variables.$color-secondary;
			}
		}
		&__btn {
			position: absolute;
			top: 0;
			right: 0;
			flex-shrink: 0;
			width: 48px;
			height: 48px;
		}
	}

	@media (config.$lg-up) {
		min-height: 60px;
		&--content {
			flex: 1;

			.m-submenu {
				background-color: variables.$color-bg-light;
			}
		}
		&--component {
			position: relative;
			display: flex;
			justify-content: flex-end;
			padding-left: 150px;

			.hoverevents & #{$s}__item:hover {
				background-color: #333333;
			}

			.m-social-networks {
				align-self: center;
			}
		}
		&__list {
			flex-flow: row wrap;
			justify-content: flex-end;
		}

		&__item {
			.hoverevents &:hover {
				.m-submenu {
					display: block;
					opacity: 1;
				}
			}

			&.active #{$s}__link,
			.hoverevents &:hover #{$s}__link {
				#{$s}--content & {
					color: variables.$color-primary;
				}
			}
		}

		&__link {
			display: flex;
			align-items: center;
			min-height: 60px;

			#{$s}--content & {
				color: variables.$color-primary;
			}
		}
		&__link-icon {
			display: block;
			margin-left: 8px;
		}
		&__btn {
			display: none;
		}
	}

	@media (config.$xl-up) {
		&__link {
			padding-right: 10px;
			padding-left: 10px;
		}
	}
	// @media (config.$lg-up) {
	// 	&__link {
	// 		padding-inline: 15px;
	// 	}
	// }
}
