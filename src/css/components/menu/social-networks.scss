@use 'config';
@use 'base/variables';
@use 'base/extends';

.m-social-networks {
	$s: &;
	&__list {
		@extend %reset-ul;
		margin: 0;

		&--horizontal {
			display: flex;

			#{$s}__link {
				display: flex;
				justify-content: center;
				align-items: center;
				min-width: 32px;
				height: 48px;
				padding: 0 5px;
				color: inherit;
				transition: color variables.$t;

				.hoverevents &:hover {
					color: variables.$color-secondary;
					text-decoration: none;
				}
			}
		}

		&--vertical {
			#{$s}__item {
				margin-bottom: 8px;

				&:last-of-type {
					margin-bottom: 0;
				}
			}

			#{$s}__link {
				position: relative;
				display: inline-block;
				padding-left: 30px;
			}

			.icon-svg {
				position: absolute;
				top: 0;
				left: 0;
				color: variables.$color-secondary;
			}
		}
	}

	&__item {
		@extend %reset-ul-li;
		margin: 0;
		padding: 0;
		background: none;
	}
	&__link {
		text-decoration: none;

		.hoverevents &:hover {
			#{$s}__link-text {
				text-decoration: none;
			}
		}
	}

	&__link-text {
		text-decoration: underline;
		text-decoration-thickness: 1px;
		text-underline-offset: 0.1em;
	}

	&__text {
		display: block;
	}

	.icon-svg {
		flex-shrink: 0;
		width: 1.5em;
	}

	// MQs
	@media (config.$lg-down) {
		.header & {
			position: absolute;
			right: 0;
			bottom: 0;
			left: 0;
			padding: 0 10px;
			border-top: 1px solid rgba(#ffffff, 0.6);
		}
	}
}
