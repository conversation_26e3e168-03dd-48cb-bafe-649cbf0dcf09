@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-carousel {
	$s: &;
	position: relative;
	max-width: variables.$row-main-width - variables.$grid-gutter * 2;
	margin: 0 auto;

	&__img {
		position: relative;
		display: block;
	}
	&__inner {
		height: 100%;
	}
	&__content {
		padding: 20px 15px;
	}
	&--homepage,
	&--category {
		img {
			position: absolute;
			top: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	&--homepage {
		#{$s}__img {
			height: 0;
			padding-top: calc(390 / 750 * 100%);
			overflow: hidden;
		}
	}

	&--category {
		#{$s}__img {
			height: 0;
			padding-top: calc(250 / 750 * 100%);
			overflow: hidden;
		}
	}

	&--category-small {
		.hoverevents & #{$s}__btns {
			opacity: 0;
			transition: opacity variables.$t ease;
		}

		.hoverevents &:hover {
			#{$s}__btns {
				opacity: 1;
			}
		}
	}

	&__viewport {
		height: 100%;
	}
	&__btns {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1;
		display: none;
		pointer-events: none;

		.is-initialized & {
			display: block;
		}
		.row-main {
			height: 100%;
		}
		.embla__btns {
			right: 0;
			bottom: 0;
			left: 0;
			display: flex;
		}
	}
	&__list {
		@extend %reset-ul;
		position: relative;
		display: flex;
		flex-wrap: nowrap;
		height: 100%;
		overflow: hidden;
		overflow-x: auto;

		.js & {
			overflow-x: hidden;
		}
	}
	&__item {
		@extend %reset-ul-li;
		flex: 0 0 auto;
		width: 100%;
		height: 100%;
	}
	&__link {
		width: 100%;
		height: 100%;
		text-decoration: none;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 1;
		}
	}

	&__item.is-active &__link,
	.hoverevents &__link:hover {
		background: variables.$color-white;
		color: variables.$color-text;
	}

	@media (config.$md-down) {
		&__inner {
			background-color: variables.$color-bg-light;
		}

		&--homepage {
			#{$s}__btns {
				bottom: auto;
				padding-top: calc(390 / 750 * 100%);
			}
		}
	}

	@media (config.$md-up) {
		&__inner {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			z-index: 0;
			display: flex;
			align-items: center;
			background-color: transparent;
			overflow: hidden;
		}
		&__content {
			max-width: 600px;
			max-height: 100%;
			padding: 20px 20px 20px 80px;
			color: #ffffff;
			box-sizing: content-box;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				z-index: -1;
				width: calc(100% + 50px);
				max-width: calc(600px + 100px + 100px);
				height: 100%;
				background: linear-gradient(90deg, rgba(#000000, 0.35) 80%, rgba(#000000, 0) 100%);
			}
		}

		&--homepage {
			#{$s}__img {
				padding-top: calc(450 / 1290 * 100%);
			}
		}

		&--category {
			#{$s}__img {
				padding-top: calc(250 / 965 * 100%);
			}
		}
	}
	@media (config.$lg-up) {
		&__content {
			h2 {
				font-size: 42px;
			}
		}
		&--homepage {
			padding-top: 20px;

			#{$s}__btns {
				top: 20px;
			}
		}
	}

	@media (config.$xl-up) {
		&--homepage {
			padding-top: 40px;

			#{$s}__btns {
				top: 40px;
			}
		}
	}
}
