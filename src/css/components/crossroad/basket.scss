@use 'config';
@use 'base/variables';

.c-basket {
	// ELEMENTS
	$s: &;
	&__table {
		.icon-svg--close {
			width: 16px;
		}

		& + #{$s}__table {
			margin-top: -1.25em;
		}
	}
	&__col {
		vertical-align: middle;
		&--price {
			width: 140px;
			text-align: right;
		}

		&--remove {
			width: 35px;
			text-align: right;
			padding-inline: 0;
		}

		&--count {
			width: 160px;
		}
	}

	&__product {
		display: flex;
		align-items: center;
		padding-left: 0;

		&-img {
			display: flex;
			flex-shrink: 0;
			justify-content: center;
			align-items: center;
			width: 40px;
			margin-right: 10px;
		}
		&-name {
			font-weight: bold;
		}

		a {
			text-decoration: none;
		}
	}

	&__total {
		border: none;

		td,
		th {
			border: none;
		}

		&--vat {
			font-size: variables.$font-size-lg;
		}

		&--novat {
			tr + & {
				border-top: 1px solid #e2ceaf;
			}
		}

		&--price {
			padding-right: 0;
			text-align: right;
		}

		&--name {
			width: 100%;
			color: variables.$color-sg-black-light;
			text-align: right;
		}
	}

	@media (config.$md-down) {
		thead {
			display: none;
		}
		tbody,
		th,
		&__table,
		&__row,
		&__cell {
			display: block;
		}

		&__table {
			border-top: 2px solid #e2ceaf;
		}

		&__row {
			position: relative;
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-end;
			padding: 10px 0;
			border-bottom: 1px solid variables.$color-secondary-light;
		}
		&__col {
			padding: 0;
			border-bottom: 0;
			&--product {
				width: 100%;
				margin-bottom: 10px;
				padding-right: 60px;
			}
			&--count {
				width: auto;
			}
			&--price {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				width: auto;
				min-width: 100px;
				padding-left: 15px;
				text-align: right;
			}
			&--price-unit {
				display: none;
			}
			#{$s}__row--product &--remove {
				position: absolute;
				top: 10px;
				right: 0;
				width: auto;
			}
		}
	}
}
